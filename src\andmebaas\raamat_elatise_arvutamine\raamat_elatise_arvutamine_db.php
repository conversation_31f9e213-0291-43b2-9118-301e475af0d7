<?php
// Raamat elatise arvutamine teenuse andmebaasi funktsioonid

require_once __DIR__ . '/../db_config.php';
require_once __DIR__ . '/../alimendid_kliendid/kliendid_handler.php';
require_once __DIR__ . '/email_templates.php';

/**
 * Salvesta raamat elatise arvutamine teenuse andmed
 */
function save_raamat_elatise_arvutamine_data($form_data, $files_data = []) {
    global $pdo;
    
    try {
        error_log("save_raamat_elatise_arvutamine_data alustatud");
        error_log("Form data: " . print_r($form_data, true));
        
        // Alusta transaktsioon
        $pdo->beginTransaction();
        
        // Arvuta valmimise kuupäev
        $valmimise_kuupaev = date('Y-m-d', strtotime('+1 day')); // Vaikimisi 1 päev
        
        // Sisesta andmed raamat_elatise_arvutamine tabelisse
        $sql = "INSERT INTO raamat_elatise_arvutamine (
            klient_nimi, klient_email,
            teenus, pakett, hind, 
            klient_kasutustingimused,
            raamat_valmimise_kuupaev, staatus
        ) VALUES (
            :klient_nimi, :klient_email,
            :teenus, :pakett, :hind,
            :klient_kasutustingimused,
            :raamat_valmimise_kuupaev, :staatus
        )";
        
        $stmt = $pdo->prepare($sql);
        
        // Määra hind
        $hind = 29.00; // Raamat elatise arvutamine teenuse hind
        
        $params = [
            ':klient_nimi' => $form_data['klient_nimi'] ?? '',
            ':klient_email' => $form_data['klient_post'] ?? '',
            ':teenus' => 'Raamat elatise arvutamine',
            ':pakett' => 'standard',
            ':hind' => $hind,
            ':klient_kasutustingimused' => isset($form_data['klient_kasutustingimused']) ? 1 : 0,
            ':raamat_valmimise_kuupaev' => $valmimise_kuupaev,
            ':staatus' => 'ootel'
        ];
        
        error_log("SQL params: " . print_r($params, true));
        
        $stmt->execute($params);
        $raamat_id = $pdo->lastInsertId();
        
        error_log("Raamat elatise arvutamine salvestatud ID-ga: " . $raamat_id);
        
        // Salvesta ka üldisesse kliendid tabelisse
        $kliendid_result = save_klient_data([
            'klient_nimi' => $form_data['klient_nimi'] ?? '',
            'klient_email' => $form_data['klient_post'] ?? '',
            'teenus' => 'Raamat elatise arvutamine',
            'pakett' => 'standard',
            'hind' => $hind
        ]);
        
        if (!$kliendid_result['success']) {
            error_log("Viga kliendi andmete salvestamisel: " . $kliendid_result['message']);
        }
        
        // Saada kinnituskiri
        try {
            send_raamat_elatise_arvutamine_confirmation_email($form_data, $raamat_id, $hind);
        } catch (Exception $e) {
            error_log("Viga e-kirja saatmisel: " . $e->getMessage());
        }
        
        // Kinnita transaktsioon
        $pdo->commit();
        
        return [
            'success' => true,
            'elatise_id' => $raamat_id,
            'message' => 'Andmed edukalt salvestatud'
        ];
        
    } catch (Exception $e) {
        // Tühista transaktsioon vea korral
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        error_log("Viga raamat elatise arvutamine andmete salvestamisel: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        
        return [
            'success' => false,
            'message' => 'Viga andmete salvestamisel: ' . $e->getMessage()
        ];
    }
}

/**
 * Loe raamat elatise arvutamine andmed ID järgi
 */
function get_raamat_elatise_arvutamine_data($id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM raamat_elatise_arvutamine WHERE id = :id");
        $stmt->execute([':id' => $id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Viga raamat elatise arvutamine andmete lugemisel: " . $e->getMessage());
        return false;
    }
}
?>
