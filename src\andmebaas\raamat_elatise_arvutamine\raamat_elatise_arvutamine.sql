-- <PERSON><PERSON><PERSON> elatise arvutamine teenuse spetsiifiline andmebaas
-- <PERSON>in hoitakse nii üldised kui ka teenuse-spetsiifilised andmed

CREATE TABLE IF NOT EXISTS raamat_elatise_arvutamine (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- <PERSON>ld<PERSON> kliendi andmed (dubleeritud alimendid_kliendid andmebaasist)
    klient_nimi VARCHAR(255) NOT NULL,
    teenus VARCHAR(100) NOT NULL DEFAULT 'Raamat elatise arvutamine',
    hind DECIMAL(10,2) NOT NULL,
    klient_email VARCHAR(255) NOT NULL,
    pakett VARCHAR(50) DEFAULT 'standard',
    klient_roll ENUM('klient', 'admin') DEFAULT 'klient',
    tellimuse_kuupaev TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    staatus ENUM('ootel', 'kinnitatud', 'tootlemisel', 'valmis', 'tühistatud') DEFAULT 'ootel',

    -- <PERSON><PERSON>t elatise arvutamine spetsiifilised väljad
    klient_kasutustingimused BOOLEAN DEFAULT FALSE,
    raamat_valmimise_kuupaev DATE,

    -- Metaandmed
    loodud TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    muudetud TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Kommentaarid tabelile
ALTER TABLE raamat_elatise_arvutamine COMMENT = 'Raamat elatise arvutamine teenuse klientide andmed ja tellimused';

-- Näidisandmed (testimiseks)
INSERT INTO raamat_elatise_arvutamine (
    klient_nimi, 
    klient_email, 
    teenus, 
    pakett, 
    hind,
    klient_roll,
    staatus,
    klient_kasutustingimused,
    raamat_valmimise_kuupaev
) VALUES (
    'Test Klient', 
    '<EMAIL>', 
    'Raamat elatise arvutamine', 
    'standard', 
    29.00,
    'klient',
    'ootel',
    1,
    DATE_ADD(CURDATE(), INTERVAL 1 DAY)
);
