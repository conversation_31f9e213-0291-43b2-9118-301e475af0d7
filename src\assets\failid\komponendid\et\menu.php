<?php
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Hardcoded paths for different depths
// This is a simple but effective approach for a site with a known structure
$scriptPath = $_SERVER['SCRIPT_NAME'];
$serverName = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : $_SERVER['SERVER_NAME'];
$isLocalhost = (strpos($serverName, 'localhost') !== false || strpos($serverName, '127.0.0.1') !== false);

// Default path (for root level)
$rootPath = './';

// For pages in the src directory
if (strpos($scriptPath, '/src/') !== false && substr_count($scriptPath, '/') === 2) {
    $rootPath = './';
}

// Special case for src/index.php
if ($scriptPath === '/src/index.php') {
    $rootPath = './';
}

// Special case for localhost:3000
if ($isLocalhost && strpos($serverName, ':3000') !== false) {
    // Always use absolute paths for localhost:3000
    $rootPath = '/src/';
}

// For pages in /et/ directory
if (strpos($scriptPath, '/et/') !== false && !strpos($scriptPath, '/et/blogi/') && !strpos($scriptPath, '/et/teenused/') && !strpos($scriptPath, '/et/raamat/') && !strpos($scriptPath, '/et/abi/') && !strpos($scriptPath, '/et/kalkulaator/')) {
    $rootPath = '../';
}

// Special case for src/et/ directory
if (strpos($scriptPath, '/src/et/') !== false && substr_count($scriptPath, '/') === 3 && !strpos($scriptPath, '/src/et/blogi/') && !strpos($scriptPath, '/src/et/teenused/') && !strpos($scriptPath, '/src/et/raamat/') && !strpos($scriptPath, '/src/et/abi/') && !strpos($scriptPath, '/src/et/kalkulaator/')) {
    $rootPath = '../';
}

// For pages in the /et/raamat/ directory structure
if (strpos($scriptPath, '/et/raamat/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/et/raamat/'
    $raamatudPos = strpos($scriptPath, '/et/raamat/');
    $remainingPath = substr($scriptPath, $raamatudPos + strlen('/et/raamat/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /et/raamat/ (e.g., /et/raamat/uudised.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /et/raamat/uudised/detail.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// Special case for src/et/raamat/ directory structure
if (strpos($scriptPath, '/src/et/raamat/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/src/et/raamat/'
    $raamatudPos = strpos($scriptPath, '/src/et/raamat/');
    $remainingPath = substr($scriptPath, $raamatudPos + strlen('/src/et/raamat/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /src/et/raamat/ (e.g., /src/et/raamat/uudised.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /src/et/raamat/uudised/detail.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}
// For pages in the /et/abi/ directory structure
if (strpos($scriptPath, '/et/abi/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/et/abi/'
    $abiPos = strpos($scriptPath, '/et/abi/');
    $remainingPath = substr($scriptPath, $abiPos + strlen('/et/abi/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /et/abi/ (e.g., /et/abi/uudised.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /et/abi/uudised/detail.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// Special case for src/et/abi/ directory structure
if (strpos($scriptPath, '/src/et/abi/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/src/et/abi/'
    $abiPos = strpos($scriptPath, '/src/et/abi/');
    $remainingPath = substr($scriptPath, $abiPos + strlen('/src/et/abi/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /src/et/abi/ (e.g., /src/et/abi/uudised.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /src/et/abi/uudised/detail.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// For pages in the /et/kalkulaator/ directory structure
if (strpos($scriptPath, '/et/kalkulaator/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/et/kalkulaator/'
    $kalkulaatorPos = strpos($scriptPath, '/et/kalkulaator/');
    $remainingPath = substr($scriptPath, $kalkulaatorPos + strlen('/et/kalkulaator/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /et/kalkulaator/ (e.g., /et/kalkulaator/elatiskalkulaator.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /et/kalkulaator/elatiskalkulaator/detail.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// Special case for src/et/kalkulaator/ directory structure
if (strpos($scriptPath, '/src/et/kalkulaator/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/src/et/kalkulaator/'
    $kalkulaatorPos = strpos($scriptPath, '/src/et/kalkulaator/');
    $remainingPath = substr($scriptPath, $kalkulaatorPos + strlen('/src/et/kalkulaator/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /src/et/kalkulaator/ (e.g., /src/et/kalkulaator/elatiskalkulaator.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /src/et/kalkulaator/elatiskalkulaator/detail.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// For pages in the /et/blogi/ directory structure
if (strpos($scriptPath, '/et/blogi/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/et/blogi/'
    $blogiPos = strpos($scriptPath, '/et/blogi/');
    $remainingPath = substr($scriptPath, $blogiPos + strlen('/et/blogi/'));
    $additionalDepth = substr_count($remainingPath, '/');

    // Special case for year directories (e.g., /et/blogi/2023/)
    if (strpos($remainingPath, '20') === 0) {
        if ($additionalDepth === 1) {
            // Files in year directory (e.g., /et/blogi/2025/article.php)
            $rootPath = '../../../../';
        } else if ($additionalDepth === 0) {
            // Year directory index (e.g., /et/blogi/2025/)
            $rootPath = '../../../';
        } else {
            // Deeper in year directory (e.g., /et/blogi/2025/subfolder/article.php)
            $rootPath = '../../../../';
        }
    } else {
        if ($additionalDepth === 0) {
            // Direct child of /et/blogi/ (e.g., /et/blogi/article.php)
            $rootPath = '../../';
        } else if ($additionalDepth === 1) {
            // One level deeper (e.g., /et/blogi/category/article.php)
            $rootPath = '../../../';
        } else if ($additionalDepth >= 2) {
            // Two or more levels deeper
            $rootPath = '../../../../';
        }
    }
}

// Special case for src/et/blogi/ directory structure
if (strpos($scriptPath, '/src/et/blogi/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/src/et/blogi/'
    $blogiPos = strpos($scriptPath, '/src/et/blogi/');
    $remainingPath = substr($scriptPath, $blogiPos + strlen('/src/et/blogi/'));
    $additionalDepth = substr_count($remainingPath, '/');

    // Special case for year directories (e.g., /src/et/blogi/2023/)
    if (strpos($remainingPath, '20') === 0) {
        if ($additionalDepth === 1) {
            // Files in year directory (e.g., /src/et/blogi/2025/article.php)
            $rootPath = '../../../';
        } else if ($additionalDepth === 0) {
            // Year directory index (e.g., /src/et/blogi/2025/)
            $rootPath = '../../';
        } else {
            // Deeper in year directory (e.g., /src/et/blogi/2025/subfolder/article.php)
            $rootPath = '../../../';
        }
    } else {
        if ($additionalDepth === 0) {
            // Direct child of /src/et/blogi/ (e.g., /src/et/blogi/article.php)
            $rootPath = '../';
        } else if ($additionalDepth === 1) {
            // One level deeper (e.g., /src/et/blogi/category/article.php)
            $rootPath = '../../';
        } else if ($additionalDepth >= 2) {
            // Two or more levels deeper
            $rootPath = '../../../';
        }
    }
}

// We'll use a more specific approach for each section rather than general path depth

// For pages in the /et/teenused/ directory structure
if (strpos($scriptPath, '/et/teenused/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/et/teenused/'
    $teenusedPos = strpos($scriptPath, '/et/teenused/');
    $remainingPath = substr($scriptPath, $teenusedPos + strlen('/et/teenused/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /et/teenused/ (e.g., /et/teenused/arvutamine.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /et/teenused/arvutamine/andmed.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// Special case for src/et/teenused/ directory structure
if (strpos($scriptPath, '/src/et/teenused/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/src/et/teenused/'
    $teenusedPos = strpos($scriptPath, '/src/et/teenused/');
    $remainingPath = substr($scriptPath, $teenusedPos + strlen('/src/et/teenused/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /src/et/teenused/ (e.g., /src/et/teenused/arvutamine.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /src/et/teenused/arvutamine/andmed.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// For pages in the /et/kalkulaatorid/ directory structure
if (strpos($scriptPath, '/et/kalkulaatorid/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/et/kalkulaatorid/'
    $kalkulaatoridPos = strpos($scriptPath, '/et/kalkulaatorid/');
    $remainingPath = substr($scriptPath, $kalkulaatoridPos + strlen('/et/kalkulaatorid/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /et/kalkulaatorid/ (e.g., /et/kalkulaatorid/arvutamine.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /et/kalkulaatorid/arvutamine/andmed.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// Special case for src/et/kalkulaatorid/ directory structure
if (strpos($scriptPath, '/src/et/kalkulaatorid/') !== false) {
    // Check if we're in a deeper nested directory by looking for additional slashes after '/src/et/kalkulaatorid/'
    $kalkulaatoridPos = strpos($scriptPath, '/src/et/kalkulaatorid/');
    $remainingPath = substr($scriptPath, $kalkulaatoridPos + strlen('/src/et/kalkulaatorid/'));
    $additionalDepth = substr_count($remainingPath, '/');

    if ($additionalDepth === 0) {
        // Direct child of /src/et/kalkulaatorid/ (e.g., /src/et/kalkulaatorid/arvutamine.php)
        $rootPath = '../../';
    } else if ($additionalDepth === 1) {
        // One level deeper (e.g., /src/et/kalkulaatorid/arvutamine/andmed.php)
        $rootPath = '../../../';
    } else if ($additionalDepth >= 2) {
        // Two or more levels deeper
        $rootPath = '../../../../';
    }
}

// Set assetsPath to be the same as rootPath for backward compatibility
$assetsPath = $rootPath;

// Check if we're in the Abi section
$isAbiSection = (strpos($scriptPath, '/et/abi/') !== false || strpos($scriptPath, '/src/et/abi/') !== false);

// Check if we're in the Kalkulaator section
$isKalkulaatorSection = (strpos($scriptPath, '/et/kalkulaator/') !== false || strpos($scriptPath, '/src/et/kalkulaator/') !== false);

// Check if we're in the Teenused section (only direct children of /et/teenused/)
$isTeenusedSection = false;
if (strpos($scriptPath, '/et/teenused/') !== false) {
    $teenusedPos = strpos($scriptPath, '/et/teenused/');
    $remainingPath = substr($scriptPath, $teenusedPos + strlen('/et/teenused/'));
    $additionalDepth = substr_count($remainingPath, '/');
    $isTeenusedSection = ($additionalDepth === 0);
} else if (strpos($scriptPath, '/src/et/teenused/') !== false) {
    $teenusedPos = strpos($scriptPath, '/src/et/teenused/');
    $remainingPath = substr($scriptPath, $teenusedPos + strlen('/src/et/teenused/'));
    $additionalDepth = substr_count($remainingPath, '/');
    $isTeenusedSection = ($additionalDepth === 0);
}

// Check if we're in the Kalkulaatorid section
$isKalkulaatoridSection = (
    strpos($scriptPath, '/et/kalkulaatorid/') !== false ||
    strpos($scriptPath, '/src/et/kalkulaatorid/') !== false ||
    strpos($scriptPath, '/et/kalkulaator/') !== false ||
    strpos($scriptPath, '/src/et/kalkulaator/') !== false
);



// For debugging
echo "<!-- Script: {$scriptPath}, Server: {$serverName}, IsLocalhost: " . ($isLocalhost ? 'true' : 'false') . ", Root: {$rootPath} -->";
?>

<!-- ========== HEADER ========== -->
<header
    class="flex flex-wrap md:justify-start md:flex-nowrap z-50 w-full">
    <nav
        class="relative max-w-6xl w-full mx-auto md:flex md:items-center md:justify-between md:gap-3 py-2 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center gap-x-1">
            <a
                class="flex-none focus:outline-none"
                href="<?php echo $rootPath; ?>index.php"
                aria-label="Alimendid.ee">
                <img
                    src="<?php echo $rootPath; ?>assets/failid/logo/alimendid.svg"
                    alt="Alimendid.ee logo"
                    width="155"
                    height="47" />
            </a>

            <!-- Collapse Button -->
            <button
                type="button"
                class="hs-collapse-toggle md:hidden relative size-9 flex justify-center items-center font-medium text-sm rounded-lg border border-gray-200 text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none"
                id="hs-header-base-collapse"
                aria-expanded="false"
                aria-controls="hs-header-base"
                aria-label="Toggle navigation"
                data-hs-collapse="#hs-header-base">
                <svg
                    class="hs-collapse-open:hidden size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round">
                    <line x1="3" x2="21" y1="6" y2="6" />
                    <line x1="3" x2="21" y1="12" y2="12" />
                    <line x1="3" x2="21" y1="18" y2="18" />
                </svg>
                <svg
                    class="hs-collapse-open:block shrink-0 hidden size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="M18 6 6 18" />
                    <path d="m6 6 12 12" />
                </svg>
                <span class="sr-only">Toggle navigation</span>
            </button>
            <!-- End Collapse Button -->
        </div>

        <!-- Collapse -->
        <div
            id="hs-header-base"
            class="hs-collapse hidden overflow-hidden transition-all duration-300 basis-full grow md:block"
            aria-labelledby="hs-header-base-collapse">
            <div
                class="overflow-hidden overflow-y-auto max-h-[75vh] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                <div
                    class="py-2 md:py-0 flex flex-col md:flex-row md:items-center gap-0.5 md:gap-1">
                    <div class="grow">
                        <div
                            class="flex flex-col md:flex-row md:justify-end md:items-center gap-0.5 md:gap-1">

                            <!-- // LINK - Avaleht -->
                            <a
                                class="p-2 flex items-center text-sm <?php echo ($currentPage == 'index') ? 'text-orange-500 font-semibold' : ''; ?> text-gray-800 hover:hover:text-orange-500 focus:outline-hidden focus:text-orange-500"
                                href="<?php echo $rootPath; ?>index.php">
                                <svg
                                    class="shrink-0 size-4 me-3 md:me-2 block md:hidden"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                                    <path
                                        d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                                </svg>
                                Avaleht
                            </a>

                            <!-- // SECTION - Teenused -->
                            <!-- Mega Menu -->
                            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] [--is-collapse:true] md:[--is-collapse:false] ">
                                <button id="hs-header-base-mega-menu-fullwidth" type="button" class="hs-dropdown-toggle w-full p-2 flex items-center text-sm <?php echo ($isTeenusedSection) ? 'text-orange-500 font-semibold' : 'text-gray-800'; ?> hover:hover:text-orange-500 focus:outline-hidden focus:bg-gray-100" aria-haspopup="menu" aria-expanded="false" aria-label="Mega Menu">
                                    <svg class="shrink-0 size-4 me-3 md:me-2 block md:hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z" />
                                        <path d="m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z" />
                                        <path d="M7 21h10" />
                                        <path d="M12 3v18" />
                                        <path d="M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2" />
                                    </svg>
                                    Teenused
                                    <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 shrink-0 size-4 ms-auto md:ms-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m6 9 6 6 6-6" />
                                    </svg>
                                </button>

                                <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] md:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full min-w-60 hidden z-10 top-full start-0 before:absolute before:-top-5 before:start-0 before:w-full before:h-5" role="menu" aria-orientation="vertical" aria-labelledby="hs-header-base-mega-menu-fullwidth">
                                    <div class="md:mx-6 lg:mx-8 md:bg-white md:rounded-lg md:shadow-md">
                                        <!-- Grid -->
                                        <div class="py-1 md:p-2 md:grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <div class="flex flex-col">

                                                <!-- // LINK - Elatise arvutamine -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'arvutamine') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/teenused/arvutamine.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect width="16" height="20" x="4" y="2" rx="2" />
                                                        <line x1="8" x2="16" y1="6" y2="6" />
                                                        <line x1="16" x2="16" y1="14" y2="18" />
                                                        <path d="M16 10h.01" />
                                                        <path d="M12 10h.01" />
                                                        <path d="M8 10h.01" />
                                                        <path d="M12 14h.01" />
                                                        <path d="M8 14h.01" />
                                                        <path d="M12 18h.01" />
                                                        <path d="M8 18h.01" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-semibold text-sm text-gray-800">Elatise arvutamine</p>
                                                        <p class="text-sm text-gray-500">Juristi poolt seadusjärgse elatissumma arvutamine</p>
                                                    </div>
                                                </a>

                                                <!-- // LINK - Elatise nõustamine -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'konsultatsioon') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/teenused/konsultatsioon.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z" />
                                                        <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-semibold text-sm text-gray-800">Juristi konsultatsioon</p>
                                                        <p class="text-sm text-gray-500">Juristi suuline nõustamine telefoni teel elatise küsimustes</p>
                                                    </div>
                                                </a>

                                                <!-- // LINK - Elatise vastus -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'vastus') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/teenused/vastus.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7" />
                                                        <rect x="2" y="4" width="20" height="16" rx="2" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-semibold text-sm text-gray-800">Kirjalik vastus</p>
                                                        <p class="text-sm text-gray-500">Juristi kirjalik vastus Sinu elatisega seotud küsimusele</p>
                                                    </div>
                                                </a>

                                            </div>
                                            <!-- End Col -->

                                            <div class="flex flex-col">

                                                <!-- // LINK - Elatise dokument -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'dokument') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/teenused/dokument.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                                                        <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                                                        <path d="M10 9H8" />
                                                        <path d="M16 13H8" />
                                                        <path d="M16 17H8" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-semibold text-sm text-gray-800">Elatise dokument</p>
                                                        <p class="text-sm text-gray-500">Elatise dokumendi koostamine esitamiseks kohtule või teisele vanemale</p>
                                                    </div>
                                                </a>

                                                <!-- // LINK - Elatise sissenõudmine -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'sissenoudmine') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/teenused/sissenoudmine.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M4 10h12" />
                                                        <path d="M4 14h9" />
                                                        <path d="M19 6a7.7 7.7 0 0 0-5.2-2A7.9 7.9 0 0 0 6 12c0 4.4 3.5 8 7.8 8 2 0 3.8-.8 5.2-2" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-semibold text-sm text-gray-800">Elatise sissenõudmine</p>
                                                        <p class="text-sm text-gray-500">Juristi poolt elatise sissenõudmine ja menetluse läbiviimine</p>
                                                    </div>
                                                </a>


                                                <!-- // LINK - Elatise õigusabi -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'oigusabi') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/teenused/oigusabi.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0" />
                                                        <path d="M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-semibold text-sm text-gray-800">Elatise õigusabi</p>
                                                        <p class="text-sm text-gray-500">Juristi õigusabi kõigis elatisega seotud küsimustes</p>
                                                    </div>
                                                </a>

                                            </div>
                                            <!-- End Col -->

                                            <!-- // LINK - Raamat -->
                                            <div class="mt-2 md:mt-0 flex flex-col">
                                                <span class="ms-2.5 mb-2 font-semibold text-xs uppercase text-gray-800">E-raamat: Elatise arvutamine</span>

                                                <a class="p-3 flex gap-x-5 items-center rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100" href="<?php echo $rootPath; ?>et/raamat/elatise-arvutamine.php">
                                                    <img class="size-32 rounded-lg" src="<?php echo $rootPath; ?>assets/failid/pildid/raamat/elatise-arvutamine/elatise-arvutamine-menu.jpg" alt="Elatise arvutamine">
                                                    <div class="grow">
                                                        <p class="text-sm text-gray-800">
                                                            Lihtne juhend, mis aitab Sul elatist korrektselt arvutada.
                                                        </p>
                                                        <p class="mt-3 inline-flex items-center gap-x-1 text-sm text-orange-500 decoration-2 group-hover:underline group-focus:underline font-medium">
                                                            Loe lähemalt
                                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                <path d="m9 18 6-6-6-6" />
                                                            </svg>
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->
                                            </div>
                                            <!-- End Col -->
                                        </div>
                                        <!-- End Grid -->
                                    </div>
                                </div>
                            </div>
                            <!-- End Mega Menu -->
                            <!-- // !SECTION - Teenused -->

                            <!-- // LINK - Meist -->
                            <a
                                class="p-2 flex items-center text-sm <?php echo ($currentPage == 'meist') ? 'text-orange-500 font-semibold' : ''; ?> text-gray-800 hover:text-orange-500 focus:outline-hidden focus:text-orange-500"
                                href="<?php echo $rootPath; ?>et/meist.php">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building2-icon lucide-building-2 shrink-0 size-4 me-3 md:me-2 block md:hidden">
                                    <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z" />
                                    <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2" />
                                    <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2" />
                                    <path d="M10 6h4" />
                                    <path d="M10 10h4" />
                                    <path d="M10 14h4" />
                                    <path d="M10 18h4" />
                                </svg>
                                Meist
                            </a>

                            <!-- // LINK - Hinnad -->
                            <a
                                class="p-2 flex items-center text-sm <?php echo ($currentPage == 'hinnad') ? 'text-orange-500 font-semibold' : ''; ?> text-gray-800 hover:text-orange-500 focus:outline-hidden focus:text-orange-500"
                                href="<?php echo $rootPath; ?>et/hinnad.php">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-badge-euro-icon lucide-badge-euro shrink-0 size-4 me-3 md:me-2 block md:hidden">
                                    <path d="M4 10h12" />
                                    <path d="M4 14h9" />
                                    <path d="M19 6a7.7 7.7 0 0 0-5.2-2A7.9 7.9 0 0 0 6 12c0 4.4 3.5 8 7.8 8 2 0 3.8-.8 5.2-2" />
                                </svg>
                                Hinnad
                            </a>

                            <!-- // LINK - Blogi -->
                            <a
                                class="p-2 flex items-center text-sm <?php echo ($currentPage == 'blogi') ? 'text-orange-500 font-semibold' : ''; ?> text-gray-800 hover:hover:text-orange-500 focus:outline-hidden focus:text-orange-500"
                                href="<?php echo $rootPath; ?>et/blogi.php">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-notebook-pen-icon lucide-notebook-pen shrink-0 size-4 me-3 md:me-2 block md:hidden">
                                    <path d="M2 6h4" />
                                    <path d="M2 10h4" />
                                    <path d="M2 14h4" />
                                    <path d="M2 18h4" />
                                    <rect width="16" height="20" x="4" y="2" rx="2" />
                                    <path d="M9.5 8h5" />
                                    <path d="M9.5 12H16" />
                                    <path d="M9.5 16H14" />
                                </svg>
                                Blogi
                            </a>

                            <!-- // SECTION - Kalkulaatorid -->
                            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] [--is-collapse:true] md:[--is-collapse:false] ">
                                <button id="hs-header-base-mega-menu-fullwidth" type="button" class="hs-dropdown-toggle w-full p-2 flex items-center text-sm <?php echo ($isKalkulaatoridSection) ? 'text-orange-500 font-semibold' : 'text-gray-800'; ?> hover:hover:text-orange-500 focus:outline-hidden focus:bg-gray-100" aria-haspopup="menu" aria-expanded="false" aria-label="Mega Menu">
                                    <svg class="shrink-0 size-4 me-3 md:me-2 block md:hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <rect width="16" height="20" x="4" y="2" rx="2" />
                                        <line x1="8" x2="16" y1="6" y2="6" />
                                        <line x1="16" x2="16" y1="14" y2="18" />
                                        <path d="M16 10h.01" />
                                        <path d="M12 10h.01" />
                                        <path d="M8 10h.01" />
                                        <path d="M12 14h.01" />
                                        <path d="M8 14h.01" />
                                        <path d="M12 18h.01" />
                                        <path d="M8 18h.01" />
                                    </svg>
                                    Kalkulaatorid
                                    <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 shrink-0 size-4 ms-auto md:ms-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m6 9 6 6 6-6" />
                                    </svg>
                                </button>

                                <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] md:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full min-w-60 hidden z-10 top-full start-0 before:absolute before:-top-5 before:start-0 before:w-full before:h-5" role="menu" aria-orientation="vertical" aria-labelledby="hs-header-base-mega-menu-fullwidth">
                                    <div class="md:mx-6 lg:mx-8 md:bg-white md:rounded-lg md:shadow-md">
                                        <!-- Grid -->
                                        <div class="py-1 md:p-2 md:grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <div class="flex flex-col">
                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'elatiskalkulaator') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/kalkulaator/elatiskalkulaator.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                                        <path d="M7 12h5" />
                                                        <path d="M15 9.4a4 4 0 1 0 0 5.2" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Elatiskalkulaator</p>
                                                        <p class="text-sm text-gray-500">Arvuta alaealise lapse seadusest tulenev miinimumelatis</p>
                                                    </div>
                                                </a>

                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'elatisabi-kalkulaator') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/kalkulaator/elatisabi-kalkulaator.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17" />
                                                        <path d="m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9" />
                                                        <path d="m2 16 6 6" />
                                                        <circle cx="16" cy="9" r="2.9" />
                                                        <circle cx="6" cy="5" r="3" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Elatisabi kalkulaator</p>
                                                        <p class="text-sm text-gray-500">Arvuta riigi poolt lapsele makstava elatisabi summa</p>
                                                    </div>
                                                </a>


                                            </div>
                                            <!-- End Col -->

                                            <div class="flex flex-col">

                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'peretoetuste-kalkulaator') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/kalkulaator/peretoetuste-kalkulaator.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                                        <path d="M16 3.128a4 4 0 0 1 0 7.744" />
                                                        <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                                                        <circle cx="9" cy="7" r="4" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Peretoetuste kalkulaator</p>
                                                        <p class="text-sm text-gray-500">Arvuta riigi poolt lapsele makstava peretoetuse summa</p>
                                                    </div>
                                                </a>

                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'lapse-vanuse-kalkulaator') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/kalkulaator/lapse-vanuse-kalkulaator.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M10 16c.5.3 1.2.5 2 .5s1.5-.2 2-.5" />
                                                        <path d="M15 12h.01" />
                                                        <path d="M19.38 6.813A9 9 0 0 1 20.8 10.2a2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1" />
                                                        <path d="M9 12h.01" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Lapse vanuse kalkulaator</p>
                                                        <p class="text-sm text-gray-500">Arvuta lapse vanus ja täisealiseks saamise kuupäev</p>
                                                    </div>
                                                </a>

                                            </div>
                                            <!-- End Col -->

                                            <div class="mt-2 md:mt-0 flex flex-col">
                                                <span class="ms-2.5 mb-2 font-semibold text-xs uppercase text-gray-800">E-raamat: Elatise arvutamine</span>

                                                <!-- Link -->
                                                <a class="p-3 flex gap-x-5 items-center rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100" href="<?php echo $rootPath; ?>et/raamat/elatise-arvutamine.php">
                                                    <img class="size-32 rounded-lg" src="<?php echo $rootPath; ?>assets/failid/pildid/raamat/elatise-arvutamine/elatise-arvutamine-menu.jpg" alt="Elatise arvutamine">
                                                    <div class="grow">
                                                        <p class="text-sm text-gray-800">
                                                            Lihtne juhend, mis aitab Sul elatist korrektselt arvutada.
                                                        </p>
                                                        <p class="mt-3 inline-flex items-center gap-x-1 text-sm text-orange-500 decoration-2 group-hover:underline group-focus:underline font-medium">
                                                            Loe lähemalt
                                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                <path d="m9 18 6-6-6-6" />
                                                            </svg>
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->
                                            </div>
                                            <!-- End Col -->
                                        </div>
                                        <!-- End Grid -->
                                    </div>
                                </div>
                            </div>
                            <!-- // !SECTION - Kalkulaatorid -->

                            <!-- // SECTION - Abi -->
                            <div class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] [--is-collapse:true] md:[--is-collapse:false] ">
                                <button id="hs-header-base-mega-menu-fullwidth" type="button" class="hs-dropdown-toggle w-full p-2 flex items-center text-sm <?php echo ($isAbiSection) ? 'text-orange-500 font-semibold' : 'text-gray-800'; ?> hover:hover:text-orange-500 focus:outline-hidden focus:bg-gray-100" aria-haspopup="menu" aria-expanded="false" aria-label="Mega Menu">
                                    <svg class="shrink-0 size-4 me-3 md:me-2 block md:hidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z" />
                                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                                        <line x1="12" x2="12.01" y1="17" y2="17" />
                                    </svg>
                                    Abi
                                    <svg class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 shrink-0 size-4 ms-auto md:ms-1" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="m6 9 6 6 6-6" />
                                    </svg>
                                </button>

                                <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] md:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full min-w-60 hidden z-10 top-full start-0 before:absolute before:-top-5 before:start-0 before:w-full before:h-5" role="menu" aria-orientation="vertical" aria-labelledby="hs-header-base-mega-menu-fullwidth">
                                    <div class="md:mx-6 lg:mx-8 md:bg-white md:rounded-lg md:shadow-md">
                                        <!-- Grid -->
                                        <div class="py-1 md:p-2 md:grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <div class="flex flex-col">
                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'uudised') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/abi/uudised.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M15 18h-5" />
                                                        <path d="M18 14h-8" />
                                                        <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-4 0v-9a2 2 0 0 1 2-2h2" />
                                                        <rect width="8" height="4" x="10" y="6" rx="1" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Uudised</p>
                                                        <p class="text-sm text-gray-500">Ülevaade elatisega seotud uudistest ja meediakajastusest</p>
                                                    </div>
                                                </a>

                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'toetused') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/abi/toetused.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17" />
                                                        <path d="m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9" />
                                                        <path d="m2 16 6 6" />
                                                        <circle cx="16" cy="9" r="2.9" />
                                                        <circle cx="6" cy="5" r="3" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Toetused</p>
                                                        <p class="text-sm text-gray-500">Teave riigi poolt lapsele makstavate toetuste kohta</p>
                                                    </div>
                                                </a>


                                            </div>
                                            <!-- End Col -->

                                            <div class="flex flex-col">

                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'elatismiinimum') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/abi/elatismiinimum.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M4 10h12" />
                                                        <path d="M4 14h9" />
                                                        <path d="M19 6a7.7 7.7 0 0 0-5.2-2A7.9 7.9 0 0 0 6 12c0 4.4 3.5 8 7.8 8 2 0 3.8-.8 5.2-2" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Elatismiinimum</p>
                                                        <p class="text-sm text-gray-500">Selgitused riigi kehtestatud elatise miinimumi kohta</p>
                                                    </div>
                                                </a>

                                                <!-- Link -->
                                                <a
                                                    class="p-2 md:p-3 flex gap-x-4 <?php echo ($currentPage == 'seadused') ? 'bg-gray-100' : ''; ?> text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 mb-1"
                                                    href="<?php echo $rootPath; ?>et/abi/seadused.php">
                                                    <svg class="shrink-0 size-4 mt-1 text-gray-800" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <path d="M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0" />
                                                        <path d="M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0" />
                                                    </svg>
                                                    <div class="grow">
                                                        <p class="font-medium text-sm text-gray-800">Seadused</p>
                                                        <p class="text-sm text-gray-500">Elatisega seotud olulisemad seadused ja õigusaktid</p>
                                                    </div>
                                                </a>

                                            </div>
                                            <!-- End Col -->

                                            <div class="mt-2 md:mt-0 flex flex-col">
                                                <span class="ms-2.5 mb-2 font-semibold text-xs uppercase text-gray-800">E-raamat: Elatise arvutamine</span>

                                                <!-- Link -->
                                                <a class="p-3 flex gap-x-5 items-center rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100" href="<?php echo $rootPath; ?>et/raamat/elatise-arvutamine.php">
                                                    <img class="size-32 rounded-lg" src="<?php echo $rootPath; ?>assets/failid/pildid/raamat/elatise-arvutamine/elatise-arvutamine-menu.jpg" alt="Elatise arvutamine">
                                                    <div class="grow">
                                                        <p class="text-sm text-gray-800">
                                                            Lihtne juhend, mis aitab Sul elatist korrektselt arvutada.
                                                        </p>
                                                        <p class="mt-3 inline-flex items-center gap-x-1 text-sm text-orange-500 decoration-2 group-hover:underline group-focus:underline font-medium">
                                                            Loe lähemalt
                                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                <path d="m9 18 6-6-6-6" />
                                                            </svg>
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->
                                            </div>
                                            <!-- End Col -->
                                        </div>
                                        <!-- End Grid -->
                                    </div>
                                </div>
                            </div>
                            <!-- // !SECTION - Abi -->

                            <!-- // LINK - Kontakt -->
                            <a
                                class="p-2 flex items-center text-sm <?php echo ($currentPage == 'kontakt') ? 'text-orange-500 font-semibold' : ''; ?> text-gray-800 hover:hover:text-orange-500 focus:outline-hidden focus:text-orange-500"
                                href="<?php echo $rootPath; ?>et/kontakt.php">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-headset-icon lucide-headset shrink-0 size-4 me-3 md:me-2 block md:hidden">
                                    <path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7" />
                                    <rect x="2" y="4" width="20" height="16" rx="2" />
                                </svg>
                                Kontakt
                            </a>
                        </div>
                    </div>

                    <div class="my-2 md:my-0 md:mx-2">
                        <div
                            class="w-full h-px md:w-px md:h-4 bg-gray-100 md:bg-gray-300"></div>
                    </div>



                    <!-- Button Group -->
                    <div class="flex flex-wrap items-center gap-x-1.5">
                        <!-- // LINK - Keel -->
                        <div
                            class="hs-dropdown [--strategy:static] md:[--strategy:fixed] [--adaptive:none] md:[--adaptive:adaptive] [--is-collapse:true] md:[--is-collapse:false]">
                            <button type="button" class="flex items-center gap-x-1.5 text-start text-xs text-gray-500 hover:text-orange-500 focus:outline-hidden focus:text-gray-800" data-hs-overlay="#hs-pro-shmnrsm">
                                <img class="shrink-0 size-3.5 rounded-full" src="<?php echo $rootPath; ?>assets/vendor/svg-country-flags/png250px/ee.png" alt="Eesti keel">
                                <!-- Kui tahad lipu järel teksti -->
                            </button>
                            <div
                                class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] md:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full md:w-52 hidden z-10 top-full ps-7 md:ps-0 md:bg-white md:rounded-lg md:shadow-md before:absolute before:-top-4 before:start-0 before:w-full before:h-5 md:after:hidden after:absolute after:top-1 after:start-4.5 after:w-0.5 after:h-[calc(100%-4px)] after:bg-gray-100"
                                role="menu"
                                aria-orientation="vertical"
                                aria-labelledby="hs-header-base-dropdown">
                                <div class="py-1 md:px-1 space-y-0.5">
                                    <a
                                        class="p-2 md:px-3 flex items-center text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100"
                                        href="<?php echo $rootPath; ?>ru/index.php">
                                        <img class="shrink-0 size-3.5 rounded-full" src="<?php echo $rootPath; ?>assets/vendor/svg-country-flags/png250px/ru.png" alt="Vene keel">
                                        По-русски
                                    </a>

                                </div>
                            </div>
                        </div>

                        <a
                            class="flex flex-col justify-center items-center size-7 md:size-9 rounded-full text-sm text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                            href="https://facebook.com/alimendid.ee" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-facebook shrink-0 size-4">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                            </svg>
                            <span class="sr-only">Facebook</span>
                        </a>

                        <a
                            class="flex flex-col justify-center items-center size-7 md:size-9 rounded-full text-sm text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                            href="https://www.instagram.com/alimendid.ee" target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-instagram shrink-0 size-4">
                                <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                                <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                            </svg>
                            <span class="sr-only">Instagram</span>
                        </a>

                        <!-- //LINK ‧ Nupp -->
                        <a class="nupp-kandiline-menu" href="<?php echo $rootPath; ?>et/alusta.php">
                            Alusta siit
                        </a>


                    </div>
                    <!-- End Button Group -->
                </div>
            </div>
        </div>
        <!-- End Collapse -->
    </nav>
</header>
<!-- ========== END HEADER ========== -->