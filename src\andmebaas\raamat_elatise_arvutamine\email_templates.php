<?php
// <PERSON><PERSON>t elatise arvutamine teenuse e-maili mallid
require_once __DIR__ . '/../email_functions.php';

function send_raamat_elatise_arvutamine_confirmation_email($form_data, $raamat_id, $hind) {
    global $alimendid_teenus;
    
    $subject = "Raamat elatise arvutamine tellimus kinnitatud - Alimendid.ee";
    
    $tahtpaev = $alimendid_teenus['raamat-elatise-arvutamine']['tahtpaev'];
    $makselink = $alimendid_teenus['raamat-elatise-arvutamine']['hind_makselink'];
    
    $html_content = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Raamat elatise arvutamine tellimus kinnitatud</title>
    </head>
    <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
        <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
            <h2 style='color: #f97316;'>Tere, " . htmlspecialchars($form_data['klient_nimi']) . "!</h2>
            
            <p>Täname teid tellimuse eest! Oleme teie andmed kätte saanud ja alustame raamatu koostamisega.</p>
            
            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #f97316;'>Tellimuse üksikasjad:</h3>
                <p><strong>Teenus:</strong> Raamat elatise arvutamine</p>
                <p><strong>Tellimuse number:</strong> #" . $raamat_id . "</p>
                <p><strong>Hind:</strong> " . $hind . "€</p>
                <p><strong>Valmimise tähtaeg:</strong> " . $tahtpaev . "</p>
            </div>
            
            <p><strong>Järgmised sammud:</strong></p>
            <ol>
                <li>Tasuge teenustasu alloleva lingi kaudu</li>
                <li>Pärast makse laekumist alustame raamatu koostamisega</li>
                <li>Saadame valmis raamatu teile e-posti teel</li>
            </ol>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . $makselink . "' style='background-color: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>Maksa " . $hind . "€</a>
            </div>
            
            <p style='font-size: 14px; color: #666;'>
                Kui teil on küsimusi, võtke meiega ühendust e-<NAME_EMAIL>
            </p>
            
            <p>Parimate soovidega,<br>
            Alimendid.ee meeskond</p>
        </div>
    </body>
    </html>";
    
    return send_email($form_data['klient_post'], $form_data['klient_nimi'], $subject, $html_content);
}
?>
