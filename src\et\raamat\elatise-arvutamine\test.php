<?php
// Lihtne test, et kontrollida, kas failid laadivad
echo "<h1>Test leht</h1>";

try {
    require_once '../../teenused/alimendid-teenused.php';
    echo "<p>✓ alimendid-teenused.php laaditud edukalt</p>";
    
    if (isset($alimendid_teenus['raamat-elatise-arvutamine'])) {
        echo "<p>✓ raamat-elatise-arvutamine teenus leitud</p>";
        echo "<p>Hind: " . $alimendid_teenus['raamat-elatise-arvutamine']['hind_kokku'] . "€</p>";
        echo "<p>Tähtaeg: " . $alimendid_teenus['raamat-elatise-arvutamine']['tahtpaev'] . "</p>";
    } else {
        echo "<p>❌ raamat-elatise-arvutamine teenust ei leitud</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Viga: " . $e->getMessage() . "</p>";
}

try {
    require_once '../../../andmebaas/db_config.php';
    echo "<p>✓ db_config.php laaditud edukalt</p>";
} catch (Exception $e) {
    echo "<p>❌ Andmebaasi viga: " . $e->getMessage() . "</p>";
}

try {
    require_once '../../../andmebaas/raamat_elatise_arvutamine/raamat_elatise_arvutamine_db.php';
    echo "<p>✓ raamat_elatise_arvutamine_db.php laaditud edukalt</p>";
} catch (Exception $e) {
    echo "<p>❌ DB funktsioonide viga: " . $e->getMessage() . "</p>";
}
?>
