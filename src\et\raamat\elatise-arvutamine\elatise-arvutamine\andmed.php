    <!-- Alimendid.ee andmebaas -->
    <?php
    require_once '../alimendid-teenused.php';
    require_once '../../../andmebaas/db_config.php';
    require_once '../../../andmebaas/oigusabi/oigusabi_db.php';

    // Käsitle vormi esitamist
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
      // Debug: logi POST andmed
      error_log("POST andmed: " . print_r($_POST, true));

      $result = save_oigusabi_data($_POST, $_FILES);

      if ($result['success']) {
        // Suuna kinnitus lehele
        header("Location: kinnitus.php?id=" . $result['elatise_id']);
        exit;
      } else {
        $error_message = $result['message'];
      }
    }
    ?>


    <!DOCTYPE html>
    <html lang="et" class="relative min-h-full">

    <head>
      <meta charset="utf-8">
      <meta name="robots" content="index, follow">
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
      <meta name="description" content="Juristi kirjalik vastus elatise küsimusele">
      <link rel="canonical" href="https://alimendid.ee/et/teenused/vastus/andmed.php">
      <meta property="og:type" content="website">
      <meta property="og:url" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
      <meta property="og:title" content="Vastus - Andmed · Alimendid.ee">
      <meta property="og:description" content="Juristi kirjalik vastus elatise küsimusele">
      <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
      <meta property="twitter:card" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
      <meta property="twitter:url" content="https://alimendid.ee/et/teenused/vastus/andmed.php">
      <meta property="twitter:title" content="Vastus - Andmed · Alimendid.ee">
      <meta property="twitter:description" content="Juristi kirjalik vastus elatise küsimusele">
      <meta property="twitter:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">

      <title>Vastus - Andmed · Alimendid.ee</title>

      <!-- Favicon -->
      <link rel="shortcut icon" href="../../../assets/failid/favicon/favicon.ico">

      <!-- Font -->
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

      <!-- CSS HS -->
      <link href="../../../output.css" rel="stylesheet" />
      <link rel="stylesheet" href="../../../assets/css/main.min.css?v=3.1.0">

      <!-- Theme Check and Update -->
      <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
      </script>

      <!-- Google Ads-->
      <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
          dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-221277240-1');
      </script>

      <!-- Google tag (gtag.js) -->
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
      <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
          dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-C8JJCED3EQ');
      </script>

      <!-- Google Tag Manager -->
      <script>
        (function(w, d, s, l, i) {
          w[l] = w[l] || [];
          w[l].push({
            'gtm.start': new Date().getTime(),
            event: 'gtm.js'
          });
          var f = d.getElementsByTagName(s)[0],
            j = d.createElement(s),
            dl = l != 'dataLayer' ? '&l=' + l : '';
          j.async = true;
          j.src =
            'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
          f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
      </script>

      <!-- Hotjar -->
      <script>
        (function(h, o, t, j, a, r) {
          h.hj = h.hj || function() {
            (h.hj.q = h.hj.q || []).push(arguments)
          };
          h._hjSettings = {
            hjid: 3283746,
            hjsv: 6
          };
          a = o.getElementsByTagName('head')[0];
          r = o.createElement('script');
          r.async = 1;
          r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
          a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
      </script>
    </head>

    <body class="dark:bg-neutral-900">

      <!-- Google Tag Manager -->
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
          style="display:none;visibility:hidden"></iframe></noscript>

      <header class="">
        <!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

        <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
      </header>

      <!-- ========== MAIN CONTENT ========== -->
      <main id="content">

        <!-- //LINK - Vormi laius -->
        <div class="max-w-xl px-4 sm:px-6 lg:px-8 py-6 md:py-12 mx-auto">

          <!-- //LINK - Pealkiri -->
          <div class="mb-6 sm:mb-10 max-w-2xl text-center mx-auto">
            <h1 class="font-semibold text-black text-4xl md:text-6xl lg:text-4xl dark:text-white pb-2">
              Elatise arvutamise juhend
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">
              Lihtne juhend elatise arvutamiseks
            </p>
          </div>

          <?php if (isset($error_message)): ?>
            <!-- Error Message -->
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Viga andmete salvestamisel</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p><?php echo htmlspecialchars($error_message); ?></p>
                  </div>
                </div>
              </div>
            </div>
          <?php endif; ?>

          <!-- Form -->
          <form id="elatise-form" method="POST" enctype="multipart/form-data">

            <!-- //LINK - Laius -->
            <div class="lg:max-w-xl mx-auto lg:mx-auto">

              <!-- Card -->
              <div class="">

                <div class="last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">

                  <!-- LINK ‧ Selgitus -->
                  <div class="pb-0 last:pb-0 last:mb-0 last:border-b-0 dark:border-neutral-700">
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                      Teenus sisaldab juristi poolt koostatud kirjalikku <span class="font-semibold pohivarv">õigusabi</span> Sinu elatisega seotud küsimustele.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">
                      Õigusabi saamiseks sisesta andmed, esita küsimus ja lisa vajadusel dokumendid. Mida rohkem infot jagad, seda täpsemalt saame Sinu küsimusele vastata.
                    </p>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                      Pärast andmete sisestamist suunatakse Sind makselehele, kus saad teenustasu <span class="font-semibold pohivarv"><?php echo $alimendid_teenus['oigusabi']['hind_kokku']; ?>€</span> tasuda pangalingi kaudu. Makse laekumise järel tutvub jurist Sinu olukorraga ja saadab vastuse e-posti teel kuni <span class="font-semibold pohivarv"><?php echo $alimendid_teenus['oigusabi']['tahtpaev']; ?></span>. Vajadusel võtame Sinuga ühendust, et täpsustada asjaolusid.
                    </p>
                  </div>
                  <!-- //SECTION - ANDMED -->
                  <div class="space-y-4">
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6">
              <div>
                <label for="eesnimi" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                  Nimi
                </label>

              </div>

              <div>
              <label for="email" class="block mb-2 text-md text-gray-800 dark:text-neutral-200">
                  E-post
                </label>

              </div>
            </div>
            <!-- End Grid -->
                    <h2 class="pt-1 mb-0 font-medium text-gray-800 dark:text-neutral-200" style="font-size: 22px;">
                      Minu andmed
                    </h2>
                    <p class="mb-3 pb-0 text-base text-gray-500 dark:text-neutral-400 max-w-md">
                      Sisesta enda andmed
                    </p>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-4">
                      <!-- //LINK - Nimi -->
                      <div>
                        <input name="klient_nimi" id="id-klient-nimi" type="text" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="Nimi" aria-label="Nimi" required>
                      </div>
                      <!-- //LINK - E-post -->
                      <div>
                        <input name="klient_post" id="id-klient-epost" type="email" class="py-3 px-4 block w-full border-gray-200 rounded-xl placeholder:text-gray-400 ring-1 ring-transparent focus:border-orange-500 focus:ring-orange-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600" placeholder="E-post" aria-label="E-post">
                      </div>
                      <!-- //LINK - Telefon -->

                    </div>
                    <!-- //!SECTION - ANDMED -->


                  </div>

                </div>

                <!-- SECTION ‧ FOOTER -->
                <!-- LINK ‧ Kasutustingimused -->
                <div class="mt-5 flex items-center">
                  <div class="flex">
                    <input name="klient_kasutustingimused" id="tingimused" type="checkbox" class="shrink-0 mt-0.5 border-gray-200 rounded-sm text-orange-500 focus:ring-transparent" value="Nõustunud">
                  </div>
                  <div class="ms-3">
                    <label for="tingimused" class="text-sm">Olen tutvunud ja nõustun <a type="button" class="text-orange-500 decoration-1 hover:underline font-medium" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-vertically-centered-scrollable-modal" data-hs-overlay="#hs-vertically-centered-scrollable-modal" href="#">kasutustingimustega</a>.</label>
                  </div>
                </div>
                <!-- End Checkbox -->
                <div id="hs-vertically-centered-scrollable-modal" class="hs-overlay hidden size-full fixed top-0 left-0 z-80 overflow-x-hidden overflow-y-auto pointer-events-none flex items-center justify-center" role="dialog" tabindex="-1" aria-labelledby="hs-vertically-centered-scrollable-modal-label">
                  <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all md:max-w-2xl md:w-full m-3 md:mx-auto h-[calc(100%-56px)] min-h-[calc(100%-56px)] flex items-center">
                    <div class="w-full max-h-full overflow-hidden flex flex-col bg-white border border-gray-200 shadow-2xs rounded-xl pointer-events-auto">
                      <div class="flex justify-between items-center py-3 px-4 border-b border-gray-200">
                        <h3 id="hs-vertically-centered-scrollable-modal-label" class="font-bold text-gray-800">
                          Alimendid.ee kasutustingimused
                        </h3>
                        <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-hidden focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-vertically-centered-scrollable-modal">
                          <span class="sr-only">Sulge</span>
                          <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                          </svg>
                        </button>
                      </div>
                      <div class="p-4 overflow-y-auto">
                        <div class="space-y-4">

                          <!-- Üldsätted -->
                          <div>
                            <h3 class="text-lg font-semibold text-gray-800">1. Üldsätted</h3>
                            <p class="mt-1 text-gray-800">
                              1.1. Käesolevad kasutustingimused (edaspidi "Kasutustingimused") reguleerivad internetiaadressil www.alimendid.ee asuva veebilehe (edaspidi "Veebileht") kaudu pakutava õigusabi osutamise teenuse (edaspidi "Teenus") kasutamise tingimusi.
                            </p>
                            <p class="mt-1 text-gray-800">
                              1.2. Veebilehe operaatoriks ja teenuse pakkujaks on Alimendid.ee OÜ (edaspidi "Alimendid.ee"), aadress Maakri 19, Tallinn 10145, äriregistri kood 11269840, e-<NAME_EMAIL>.
                            </p>
                            <p class="mt-1 text-gray-800">
                              1.3. Veebilehe kasutaja (edaspidi "Kasutaja") kinnitab Kasutustingimustega nõustumisel, et kõik tema poolt sisestatud andmed on õiged ja tal on kõik õigused ja volitused Teenuste kasutamiseks.
                            </p>
                          </div>

                          <!-- Teenus -->
                          <div>
                            <h3 class="text-lg font-semibold text-gray-800">2. Teenus</h3>
                            <p class="mt-1 text-gray-800">
                              2.1. Alimendid.ee võimaldab Kasutajal kasutada Veebilehe kaudu erinevaid õigusabi teenuseid, sealhulgas õigusalast nõustamist, dokumentide koostamist ja menetlusabi.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.2. Teenuse osutamiseks peab Kasutaja täitma Veebilehel ettenähtud kohustuslikud andmeväljad.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.3. Sõltuvalt valitud paketist on teenustasu suuruseks Veebilehe hinnakirjas avaldatud teenustasud. Kõikide Veebilehel müüdavate teenuste hinnad on avaldatud eurodes.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.4. Teenuse eest tasumise järel osutab Alimendid.ee lepingu esemeks oleva teenuse Veebilehel avaldatud tingimustes ja tähtaja jooksul.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.5. Teenustasu saab tasuda turvaliselt läbi Eesti pangamaksete: Swedbank, SEB, Luminor, LHV, Coop Pank, Citadele, Pocopay. Maksete vahendajaks on Maksekeskus AS. Tasumine toimub väljaspool Veebilehte vastava panga turvalises keskkonnas. Alimendid.ee'l puudub ligipääs Kasutaja panga andmetele.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.6. Alimendid.ee on Kasutaja isikuandmete vastutav töötleja. Alimendid.ee edastab maksete teostamiseks vajalikud isikuandmed volitatud töötleja Maksekeskus AS-ile.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.7. Pangalingi vahendusel makse sooritamise järel tuleb Kasutajal vajutada nupule "Tagasi kaupmehe juurde", mille järel suunatakse Kasutaja tellimuse kättesaamist kinnitavale veebilehele.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.8. Tellimuse kinnitamise järel puudub Kasutajal lepingust taganemise õigus (VÕS § 53 lg 4 p 1). Lepingu esemeks oleva teenuse osutamise ja Kasutajale kättesaadavaks tegemisega on Alimendid.ee täitnud täielikult kõik lepingust tulenevad kohustused.
                            </p>
                            <p class="mt-1 text-gray-800">
                              2.9. Veebilehe kaudu õigusabi teenuse kasutamine ei kujuta endast advokaadibüroo teenust ega asenda kohtus esindamist, välja arvatud juhul, kui see on Veebilehel eraldi märgitud.
                            </p>
                          </div>

                          <!-- Isikuandmed -->
                          <div>
                            <h3 class="text-lg font-semibold text-gray-800">3. Isikuandmed</h3>
                            <p class="mt-1 text-gray-800">
                              3.1. Veebilehte ja õigusalase nõustamise sisu puudutavad autori- ja muud õigused kuuluvad Alimendid.ee-le.
                            </p>
                            <p class="mt-1 text-gray-800">
                              3.2. Veebilehe abil koostatud dokumendid ja nende sisu on konfidentsiaalsed. Kasutajal ei ole õigust ilma Alimendid.ee eelneva kirjaliku nõusolekuta avalikult kättesaadavaks teha Alimendid.ee poolt koostatud dokumentide sisu.
                            </p>
                            <p class="mt-1 text-gray-800">
                              3.3. Alimendid.ee töötleb järgnevaid isikuandmeid: ees- ja perekonnanimi, e-posti aadress. Kasutajal on igal ajal õigus tutvuda enda poolt sisestatud andmetega ja nõuda Alimendid.ee-lt kõikide Kasutajat puudutavate ja tema poolt sisestatud andmete kustutamist.
                            </p>
                            <p class="mt-1 text-gray-800">
                              3.4. Alimendid.ee ei edasta, müü ega avalikusta Kasutaja andmeid kolmandatele osapooltele.
                            </p>
                          </div>

                          <!-- Lõppsätted -->
                          <div>
                            <h3 class="text-lg font-semibold text-gray-800">4. Lõppsätted</h3>
                            <p class="mt-1 text-gray-800">
                              4.1. Alimendid.ee-l on õigus Kasutustingimusi igal ajal muuta. Muudetud Kasutustingimused jõustuvad nende avaldamisel Veebilehel.
                            </p>
                            <p class="mt-1 text-gray-800">
                              4.2. Alimendid.ee ei vastuta otseste, kaudsete, karistuslike, faktiliste või kaasuvate kahjude eest, mis tulenevad Teenuste kasutamisest või Veebilehe sisust.
                            </p>
                            <p class="mt-1 text-gray-800">
                              4.3. Kasutustingimustest tulenevatele õigussuhetele kohaldatakse Eesti Vabariigi õigust.
                            </p>
                            <p class="mt-1 text-gray-800">
                              4.4. Võimalikke vaidlusi lahendatakse Harju Maakohtu Tallinna kohtumajas.
                            </p>
                          </div>

                        </div>
                      </div>


                      <div class="flex justify-end items-center gap-x-2 py-3 px-4 border-t border-gray-200">
                        <button id="laadi-alla-tingimused" type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                          <svg class="w-4 h-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7 10 12 15 17 10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                          </svg>
                          Laadi alla
                        </button>
                        <button type="button" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-orange-500 text-white hover:bg-orange-500 focus:outline-hidden focus:bg-orange-500 disabled:opacity-50 disabled:pointer-events-none" data-hs-overlay="#hs-vertically-centered-scrollable-modal">
                          Sulge
                        </button>

                      </div>
                    </div>
                  </div>
                </div>



                <!-- Button Group -->
                <div class="flex flex-col gap-y-2 pt-5">
                  <button type="submit" class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl border border-transparent bg-orange-500 text-lg text-white disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-[#F86A1A]"
                    style="--tw-bg-opacity: 1;"
                    onmouseover="this.style.backgroundColor='#F86A1A'"
                    onmouseout="this.style.backgroundColor=''">
                    Esitan andmed
                  </button>

                  <a href="../../../et/teenused/vastus/alusta.php" class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl border border-transparent text-orange-500 bg-orange-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-orange-50 dark:text-orange-500 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m15 18-6-6 6-6" />
                    </svg> Tagasi
                  </a>
                </div>
                <!-- End Button Group -->

              </div>
          </form>
          <!-- End Form -->
      </main>
      <!-- ========== END MAIN CONTENT ========== -->

      <!-- //SECTION - SKRIPTID -->
      <!-- JS PLUGINS -->
      <!-- Required plugins -->
      <script src="../../../assets/vendor/preline/dist/index.js?v=3.1.0"></script>
      <!-- Clipboard -->
      <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
      <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

      <!-- //LINK - Laadi alla kasutustingimused -->
      <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const laadiAllaNupp = document.getElementById('laadi-alla-tingimused');

          if (laadiAllaNupp) {
            laadiAllaNupp.addEventListener('click', function() {
              const {
                jsPDF
              } = window.jspdf;
              const doc = new jsPDF();

              doc.setFont("helvetica", "bold");
              doc.setFontSize(16);
              doc.text("Alimendid.ee kasutustingimused", 20, 20);

              doc.setFont("helvetica", "normal");
              doc.setFontSize(12);

              const tingimused = document.querySelector('#hs-vertically-centered-scrollable-modal .space-y-4');
              if (tingimused) {
                const blocks = tingimused.children;
                let yPos = 30;

                for (let block of blocks) {
                  const titleEl = block.querySelector('h3');
                  const paragraphs = block.querySelectorAll('p');

                  // Lisa pealkiri
                  if (titleEl) {
                    if (yPos > 270) {
                      doc.addPage();
                      yPos = 20;
                    }

                    doc.setFont("helvetica", "bold");
                    doc.setFontSize(14);
                    doc.text(titleEl.textContent, 20, yPos);
                    yPos += 10;
                  }

                  // Lisa kõik <p>-d
                  doc.setFont("helvetica", "normal");
                  doc.setFontSize(12);
                  for (let p of paragraphs) {
                    const text = p.textContent.trim();
                    const lines = doc.splitTextToSize(text, 170);

                    if (yPos + (lines.length * 7) > 280) {
                      doc.addPage();
                      yPos = 20;
                    }

                    doc.text(lines, 20, yPos);
                    yPos += lines.length * 5 + 2;
                  }

                  yPos += 5; // lisaruum plokkide vahel
                }
              } else {
                doc.text("Kasutustingimused ei ole saadaval. Palun külastage meie veebilehte.", 20, 30);
              }

              doc.setFont("helvetica", "italic");
              doc.setFontSize(10);
              const tänaneKuupäev = new Date().toLocaleDateString('et-EE');
              doc.text(`Alimendid.ee kasutustingimused seisuga ${tänaneKuupäev}`, 20, 280);

              doc.save("Alimendid_ee_kasutustingimused.pdf");
            });
          }
        });
      </script>

      <!-- //LINK - Failide laadimine -->
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const fileInput = document.getElementById('id-klient-dokumendid');
          const fileList = document.getElementById('file-list');
          const uploadedFiles = [];

          fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);

            if (files.length > 0) {
              fileList.classList.remove('hidden');
              fileList.innerHTML = '';

              files.forEach((file, index) => {
                // Create file item with progress
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center gap-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700';
                fileItem.innerHTML = `
                  <div class="flex-shrink-0">
                    <svg class="size-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                      <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                    </svg>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-x-2">
                      <p class="text-sm font-medium text-gray-800 truncate dark:text-neutral-200">${file.name}</p>
                      <span class="text-xs text-gray-500 dark:text-neutral-500">${(file.size / 1024).toFixed(1)} KB</span>
                    </div>
                    <div class="mt-1 flex items-center gap-x-2">
                      <div class="flex-1 bg-gray-200 rounded-full h-2 dark:bg-neutral-700">
                        <div class="progress-bar bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                      </div>
                      <span class="text-xs font-medium text-gray-800 dark:text-neutral-200 progress-text">0%</span>
                    </div>
                  </div>
                  <div class="flex-shrink-0">
                    <button type="button" class="text-gray-400 hover:text-red-500 dark:text-neutral-500 dark:hover:text-red-400" onclick="removeFile(this, ${index})">
                      <svg class="size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 6 6 18"/>
                        <path d="m6 6 12 12"/>
                      </svg>
                    </button>
                  </div>
                `;

                fileList.appendChild(fileItem);

                // Simulate upload progress
                simulateUpload(fileItem, file, index);
              });
            } else {
              fileList.classList.add('hidden');
            }
          });

          // Simulate file upload progress
          function simulateUpload(fileItem, file, index) {
            const progressBar = fileItem.querySelector('.progress-bar');
            const progressText = fileItem.querySelector('.progress-text');
            let progress = 0;

            const interval = setInterval(() => {
              progress += Math.random() * 15;
              if (progress >= 100) {
                progress = 100;
                clearInterval(interval);

                // Show success state
                setTimeout(() => {
                  fileItem.innerHTML = `
                    <div class="flex-shrink-0">
                      <svg class="size-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"/>
                        <path d="M14 2v4a2 2 0 0 0 2 2h4"/>
                      </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center gap-x-2">
                        <p class="text-sm font-medium text-gray-800 truncate dark:text-neutral-200">${file.name}</p>
                        <span class="text-xs text-gray-500 dark:text-neutral-500">${(file.size / 1024).toFixed(1)} KB</span>
                      </div>
                      <div class="mt-1 flex items-center gap-x-2">
                        <div class="flex-1 bg-gray-200 rounded-full h-2 dark:bg-neutral-700">
                          <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                        </div>
                        <span class="text-xs font-medium text-green-600 dark:text-green-400">100%</span>
                      </div>
                    </div>
                    <div class="flex-shrink-0">
                      <svg class="size-4 text-green-500" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
                      </svg>
                    </div>
                  `;
                }, 500);
              }

              progressBar.style.width = progress + '%';
              progressText.textContent = Math.round(progress) + '%';
            }, 200 + index * 100);
          }

          // Remove file function
          window.removeFile = function(button, index) {
            const fileItem = button.closest('div');
            fileItem.remove();

            // Remove from file input
            const dt = new DataTransfer();
            const files = Array.from(fileInput.files);
            files.splice(index, 1);
            files.forEach(file => dt.items.add(file));
            fileInput.files = dt.files;

            // Hide file list if empty
            if (fileList.children.length === 0) {
              fileList.classList.add('hidden');
            }
          };
        });
      </script>

      <!-- //LINK - Datepicker initialization -->
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const datepickerInput = document.getElementById('konsultatsiooni_kuupaev');

          function formatDateValue(input) {
            const value = input.value;
            console.log('formatDateValue called with:', value);
            if (value) {
              // Convert yyyy-mm-dd to dd.mm.yyyy
              if (value.match(/^\d{4}-\d{2}-\d{2}$/)) {
                const parts = value.split('-');
                const newValue = parts[2] + '.' + parts[1] + '.' + parts[0];
                input.value = newValue;
                // Force trigger change event
                input.dispatchEvent(new Event('input', {
                  bubbles: true
                }));
                return newValue;
              }
              // Convert yyyy.mm.dd to dd.mm.yyyy
              else if (value.match(/^\d{4}\.\d{2}\.\d{2}$/)) {
                const parts = value.split('.');
                const newValue = parts[2] + '.' + parts[1] + '.' + parts[0];
                input.value = newValue;
                // Force trigger change event
                input.dispatchEvent(new Event('input', {
                  bubbles: true
                }));
                return newValue;
              }
            }
            return value;
          }

          // Add multiple event listeners to catch date changes
          if (datepickerInput) {
            ['change', 'input', 'blur'].forEach(eventType => {
              datepickerInput.addEventListener(eventType, function() {
                // Use setTimeout to ensure this runs after datepicker's own handlers
                setTimeout(() => {
                  formatDateValue(this);
                }, 10);
              });
            });

            // Use MutationObserver to watch for value changes
            const observer = new MutationObserver(function(mutations) {
              mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                  setTimeout(() => {
                    formatDateValue(datepickerInput);
                  }, 10);
                }
              });
            });

            observer.observe(datepickerInput, {
              attributes: true,
              attributeFilter: ['value']
            });

            // Also watch for property changes
            let lastValue = datepickerInput.value;
            setInterval(() => {
              if (datepickerInput.value !== lastValue) {
                lastValue = datepickerInput.value;
                setTimeout(() => {
                  formatDateValue(datepickerInput);
                }, 10);
              }
            }, 100);
          }

          // Add form submit listener to debug date value
          const form = document.getElementById('elatise-form');
          if (form) {
            form.addEventListener('submit', function(e) {
              const dateInput = document.getElementById('konsultatsiooni_kuupaev');
              console.log('Form submitting with date value:', dateInput.value);

              // Ensure date is in correct format before submit
              if (dateInput.value) {
                formatDateValue(dateInput);
                console.log('Final date value before submit:', dateInput.value);
              }
            });
          }

          // Initialize datepicker with Estonian locale
          if (window.HSDatepicker) {
            // First auto-init all datepickers
            window.HSDatepicker.autoInit();

            // Then customize each datepicker
            const datepickers = document.querySelectorAll('.hs-datepicker');
            datepickers.forEach(function(datepicker) {
              const instance = window.HSDatepicker.getInstance(datepicker, true);
              if (instance && instance.element && instance.element.vanillaCalendar) {
                // Set Estonian locale
                instance.element.vanillaCalendar.settings.locale = 'et';
                instance.element.vanillaCalendar.settings.months = ['Jaanuar', 'Veebruar', 'Märts', 'Aprill', 'Mai', 'Juuni', 'Juuli', 'August', 'September', 'Oktoober', 'November', 'Detsember'];
                instance.element.vanillaCalendar.settings.weekdays = ['E', 'T', 'K', 'N', 'R', 'L', 'P'];
                instance.element.vanillaCalendar.settings.dateFormat = 'dd.mm.yyyy';
                instance.element.vanillaCalendar.settings.format = 'dd.mm.yyyy';

                // Update the calendar
                instance.element.vanillaCalendar.update();
              }
            });
          }
        });
      </script>

      <script>
        document.addEventListener('DOMContentLoaded', function() {
          // Leia kõik required väljad
          const requiredFields = document.querySelectorAll('input[required], select[required], textarea[required]');

          // Lisa igale väljale validatsiooni kuular
          requiredFields.forEach(field => {
            field.addEventListener('invalid', function(e) {
              e.preventDefault();

              // Kohanda teavituse tekst vastavalt välja tüübile
              let message = '';
              switch (field.name) {
                case 'klient_nimi':
                  message = 'Palun sisesta oma nimi';
                  break;
                case 'klient_post':
                  message = 'Palun sisesta oma e-posti aadress';
                  break;
                case 'klient_telefon':
                  message = 'Palun sisesta oma telefoninumber';
                  break;
                case 'konsultatsiooni_kuupaev':
                  message = 'Palun vali konsultatsiooni kuupäev';
                  break;
                case 'konsultatsiooni_kellaaeg':
                  message = 'Palun vali konsultatsiooni kellaaeg';
                  break;
                default:
                  message = 'See väli on kohustuslik';
              }

              // Määra kohandatud teavitus
              field.setCustomValidity(message);

              // Näita teavitust
              field.reportValidity();
            });

            // Puhasta teavitus, kui väli muutub
            field.addEventListener('input', function() {
              field.setCustomValidity('');
            });
          });
        });
      </script>
      <!-- //!SECTION - Skriptid -->

    </body>

    </html>