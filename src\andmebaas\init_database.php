<?php
// Andmebaasi initsialiseerimise skript
// Käivita see fail andmebaasi tabelite loomiseks

require_once 'db_config.php';

function init_database() {
    global $pdo;
    
    try {
        echo "Alustame andmebaasi initsialiseerimist...\n";
        
        // 1. Loo üldine klientide tabel
        echo "Loon klientide tabelit...\n";
        $sql = file_get_contents(__DIR__ . '/alimendid_kliendid/alimendid_kliendid.sql');
        $pdo->exec($sql);
        echo "✓ Klientide tabel loodud\n";

        // 2. Loo elatise nõustamine tabel
        echo "Loon elatise nõustamine tabelit...\n";
        $sql = file_get_contents(__DIR__ . '/konsultatsioon/konsultatsioon.sql');
        $pdo->exec($sql);
        echo "✓ Elatise nõustamine tabel loodud\n";
        
        // 3. Loo muud teenuste tabelid (kui failid eksisteerivad)
        $teenused = [
            'arvutamine',
            'dokument',
            'oigusabi',
            'sissenoudmine',
            'vastus',
            'raamat_elatise_arvutamine'
        ];
        
        foreach ($teenused as $teenus) {
            $file_path = __DIR__ . "/{$teenus}/{$teenus}.sql";
            if (file_exists($file_path)) {
                echo "Loon {$teenus} tabelit...\n";
                $sql = file_get_contents($file_path);
                $pdo->exec($sql);
                echo "✓ {$teenus} tabel loodud\n";

                // Loo teenuse dokumendid kaust
                $dokumendid_dir = __DIR__ . "/{$teenus}/dokumendid/";
                if (!is_dir($dokumendid_dir)) {
                    mkdir($dokumendid_dir, 0755, true);
                    echo "✓ {$teenus} dokumendid kaust loodud\n";
                }
            }
        }
        
        // 4. Loo elatise nõustamine dokumendid kaust (kui puudub)
        $elatise_dokumendid_dir = __DIR__ . '/konsultatsioon/dokumendid/';
        if (!is_dir($elatise_dokumendid_dir)) {
            mkdir($elatise_dokumendid_dir, 0755, true);
            echo "✓ Elatise nõustamine dokumendid kaust loodud\n";
        }
        
        echo "\n🎉 Andmebaas edukalt initsialiseeritud!\n";
        echo "Nüüd saad kasutada Alimendid.ee teenuseid.\n";
        
    } catch (Exception $e) {
        echo "❌ Viga andmebaasi initsialiseerimise käigus: " . $e->getMessage() . "\n";
        return false;
    }
    
    return true;
}

// Käivita initsialiseerimise, kui fail käivitatakse otse
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    init_database();
}
?>
