<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
    <div class="text-center">
        <!-- Pealkiri -->
        <div class="pt-10 w-full max-w-5xl px-4 sm:px-6 lg:px-8 mx-auto">
            <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
                <!-- Title -->
                <div class="max-w-2xl mx-auto text-center mb-6">
                    <h2 class="text-2xl font-semibold md:text-4xl lg:text-4xl md:leading-tight pb-1">Elatise arvutamise juhend</h2>
                    <p class="mt-1 text-gray-600 text-lg">Lihtne juhend elatise arvutamiseks</p>
                </div>
                <!-- End Title -->
            </div>
        </div>

        <!-- Pilt -->
        <div class="mb-10">
            <div class="relative group flex justify-center">
                <!-- Taustavari -->
                <div class="absolute inset-0 rounded-3xl blur-2xl opacity-20"></div>

                <!-- Peamine pilt -->
                <div class="relative">
                    <div style="width: 300px;">
                        <img
                            class="w-full h-auto rounded-2xl"
                            src="<?php echo $rootPath; ?>assets/failid/pildid/raamat/elatise-arvutamine-valge.png"
                            alt="Elatise arvutamise raamat - Alimendid.ee"
                            loading="lazy" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Nupp -->

        <!-- Nupp -->
        <div class="text-center">
            <a class="nupp-umar-noolega" href="<?php echo $rootPath; ?>et/raamat/elatise-arvutamine.php">
                Loe lähemalt
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6" />
                </svg>
            </a>
        </div>
    </div>
</div>