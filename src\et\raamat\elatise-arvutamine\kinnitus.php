    <!-- Alimendid.ee andmebaas -->
    <?php
    require_once '../../teenused/alimendid-teenused.php';
    require_once '../../../andmebaas/raamat_elatise_arvutamine/raamat_elatise_arvutamine_db.php';
    ?>

    <?php
    // Muutujad andmete kuvamiseks
    $klient_nimi = '';
    $pakett = '';
    $hind = '';
    $save_result = ['success' => true]; // Eeldame, et jõudsime siia edukalt
    $elatise_id = null;

    // Kontrolli, kas ID on GET parameetris (suunatud andmed.php-st)
    if (isset($_GET['id'])) {
        $elatise_id = (int)$_GET['id'];

        // Loe andmed andmebaasist
        $data = get_raamat_elatise_arvutamine_data($elatise_id);
        if ($data) {
            $klient_nimi = $data['klient_nimi'];
            $pakett = $data['pakett'];
            $hind = $data['hind'];
        } else {
            $save_result = ['success' => false, 'message' => 'Andmeid ei leitud'];
        }
    } else {
        $save_result = ['success' => false, 'message' => 'Puudub tellimuse ID'];
    }
    ?>


    <!DOCTYPE html>
    <html lang="et" class="relative min-h-full">

    <head>
        <meta charset="utf-8">
        <meta name="robots" content="index, follow">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <meta name="description" content="Elatise arvutamise juhend tellimus kinnitatud">
        <link rel="canonical" href="https://alimendid.ee/et/raamat/elatise-arvutamine/kinnitus.php">
        <meta property="og:type" content="website">
        <meta property="og:url" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
        <meta property="og:title" content="Elatise arvutamise juhend - Kinnitus · Alimendid.ee">
        <meta property="og:description" content="Elatise arvutamise juhend tellimus kinnitatud">
        <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
        <meta property="twitter:card" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">
        <meta property="twitter:url" content="https://alimendid.ee/et/raamat/elatise-arvutamine/kinnitus.php">
        <meta property="twitter:title" content="Elatise arvutamise juhend - Kinnitus · Alimendid.ee">
        <meta property="twitter:description" content="Elatise arvutamise juhend tellimus kinnitatud">
        <meta property="twitter:image" content="https://alimendid.ee/assets/failid/pildid/meta/alimendid.jpg">

        <title>Elatise arvutamise juhend - Kinnitus · Alimendid.ee</title>

        <!-- Favicon -->
        <link rel="shortcut icon" href="../../../assets/failid/favicon/favicon.ico">

        <!-- CSS HS -->
        <link href="../../../output.css" rel="stylesheet" />
        <link href="../../../assets/css/main.min.css" rel="stylesheet" />

        <!-- Theme Check and Update -->
        <script>
            const html = document.querySelector('html');
            const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
            const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

            if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
            else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
            else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
            else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>

        <!-- Google Ads-->
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'UA-221277240-1');
        </script>

        <!-- Google tag (gtag.js) -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-C8JJCED3EQ');
        </script>

        <!-- Google Tag Manager -->
        <script>
            (function(w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({
                    'gtm.start': new Date().getTime(),
                    event: 'gtm.js'
                });
                var f = d.getElementsByTagName(s)[0],
                    j = d.createElement(s),
                    dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
        </script>

        <!-- Hotjar -->
        <script>
            (function(h, o, t, j, a, r) {
                h.hj = h.hj || function() {
                    (h.hj.q = h.hj.q || []).push(arguments)
                };
                h._hjSettings = {
                    hjid: 3283746,
                    hjsv: 6
                };
                a = o.getElementsByTagName('head')[0];
                r = o.createElement('script');
                r.async = 1;
                r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
                a.appendChild(r);
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
        </script>
    </head>

    <body class="dark:bg-neutral-900">

        <!-- Google Tag Manager -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0"
                style="display:none;visibility:hidden"></iframe></noscript>

        <header class="">
            <!-- Google Tag Manager (noscript) -->
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

            <?php include '../../../assets/failid/komponendid/et/menu.php'; ?>
        </header>

        <!-- ========== MAIN CONTENT ========== -->
        <main id="content">
            <!-- Bag -->
            <div class="max-w-xl px-4 sm:px-6 lg:px-8 py-12 lg:py-24 mx-auto">

                <?php if ($save_result && !$save_result['success']): ?>
                    <!-- Veateade -->
                    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                        <div class="flex items-center">
                            <svg class="shrink-0 size-5 text-red-500 mr-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10" />
                                <line x1="15" y1="9" x2="9" y2="15" />
                                <line x1="9" y1="9" x2="15" y2="15" />
                            </svg>
                            <div>
                                <h3 class="text-red-800 font-medium">Viga andmete salvestamisel</h3>
                                <p class="text-red-700 text-sm mt-1"><?php echo htmlspecialchars($save_result['message']); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="mb-6 sm:mb-10 max-w-2xl text-center mx-auto">
                    <div class="mb-4 flex justify-center">
                        <?php if ($save_result && $save_result['success']): ?>
                            <span class="shrink-0 size-14 md:size-16 mx-auto flex justify-center items-center border-2 border-green-500 text-green-500 rounded-full">
                                <svg class="shrink-0 size-8" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 6 9 17l-5-5" />
                                </svg>
                            </span>
                        <?php else: ?>
                            <span class="shrink-0 size-14 md:size-16 mx-auto flex justify-center items-center border-2 border-orange-500 text-orange-500 rounded-full">
                                <svg class="shrink-0 size-8" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 6 9 17l-5-5" />
                                </svg>
                            </span>
                        <?php endif; ?>
                    </div>

                    <?php if ($save_result && $save_result['success']): ?>
                        <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white">
                            Andmed on edastatud!
                        </h1>

                        <p class="mt-3 text-black dark:text-white">
                            Oleme andmed kätte saanud. Palun lõpeta tellimus, makstes teenustasu alloleva nupu kaudu.
                        </p>
                    <?php else: ?>
                        <h1 class="font-medium text-black text-3xl sm:text-4xl dark:text-white">
                            Andmed on edastatud!
                        </h1>

                        <p class="mt-3 text-black dark:text-white">
                            Oleme andmed kätte saanud. Palun lõpeta tellimus, makstes teenustasu alloleva nupu kaudu.
                        </p>
                    <?php endif; ?>
                </div>

                <div class="max-w-xl mx-auto">
                    <div class="max-w-xs mx-auto">
                        <div class="mb-10 flex items-center justify-center">
                            <a href="<?php echo $alimendid_teenus['raamat-elatise-arvutamine']['hind_makselink']; ?>" class="w-64 py-2.5 px-3.5 inline-flex justify-center items-center gap-x-2 font-medium text-nowrap rounded-xl border border-transparent bg-orange-500 text-white hover:bg-orange-600 focus:outline-hidden focus:bg-orange-600 transition disabled:opacity-50 disabled:pointer-events-none">
                                <span class="font-semibold text-white">Maksa <?php echo $alimendid_teenus['raamat-elatise-arvutamine']['hind_kokku']; ?>€</span>
                            </a>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="bg-orange-50 dark:bg-neutral-700 rounded-xl p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-neutral-200 mb-4">Tellimuse kokkuvõte</h3>

                        <div class="space-y-4">
                            <div class="flex justify-between text-gray-800 dark:text-neutral-200">
                                <span class="font-medium">Tellija nimi</span>
                                <span><?php echo htmlspecialchars($klient_nimi); ?></span>
                            </div>

                            <div class="flex justify-between text-gray-800 dark:text-neutral-200">
                                <span class="font-medium">Teenuse nimetus</span>
                                <span><?php echo $alimendid_teenus['raamat-elatise-arvutamine']['nimi']; ?></span>
                            </div>

                            <div class="flex justify-between text-gray-800 dark:text-neutral-200">
                                <span class="font-medium">Raamatu saamine</span>
                                <span>kuni <?php echo $alimendid_teenus['raamat-elatise-arvutamine']['tahtpaev']; ?></span>
                            </div>

                            <div class="flex justify-between text-lg font-semibold text-gray-900 dark:text-neutral-100 pt-4 border-t border-orange-100 dark:border-neutral-600">
                                <span>Hind</span>
                                <span class="text-orange-500"><?php echo $alimendid_teenus['raamat-elatise-arvutamine']['hind_kokku']; ?>.00€</span>
                            </div>
                        </div>
                    </div>
                    <button onclick="history.back()" class="py-3 px-3 w-full inline-flex justify-center items-center gap-x-1.5 font-medium rounded-xl border border-transparent text-orange-500 bg-orange-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-orange-50 dark:text-orange-500 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="m15 18-6-6 6-6" />
                        </svg> Tagasi
                    </button>
                    <!-- End Order Summary -->
                </div>
            </div>
            <!-- End Bag -->

        </main>
        <!-- ========== END MAIN CONTENT ========== -->

        <!-- ========== FOOTER ========== -->

        <!-- ========== END FOOTER ========== -->

        <!-- JS PLUGINS -->
        <!-- Required plugins -->
        <script src="../../../assets/vendor/preline/dist/index.js?v=3.1.0"></script>
        <!-- Clipboard -->
        <script src="../../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
        <script src="../../../assets/js/hs-copy-clipboard-helper.js"></script>

        <script src="../../../assets/vendor/canvas-confetti/dist/confetti.browser.js"></script>
        <script>
            window.addEventListener('load', () => {
                confetti({
                    particleCount: 100,
                    spread: 70,
                    origin: {
                        y: 0.6
                    }
                });
            });
        </script>

    </body>

    </html>