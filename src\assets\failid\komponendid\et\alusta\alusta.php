<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-5">

    <!-- LINK - Arvutamine -->
    <a class="p-4 md:p-6 min-h-50 group relative overflow-hidden flex flex-col justify-center items-center text-center bg-white border border-gray-200 rounded-xl transition duration-300"
        onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#1f2937'; this.querySelector('.alusta-tekst').style.color='#f97316'; this.querySelector('.alusta-tekst').style.textDecoration='underline'; this.querySelector('.alusta-tekst').style.fontWeight='600';"
        onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color=''; this.querySelector('.alusta-tekst').style.color='#6b7280'; this.querySelector('.alusta-tekst').style.textDecoration='none'; this.querySelector('.alusta-tekst').style.fontWeight='400';" href="<?php echo $rootPath; ?>et/teenused/arvutamine.php">
        <div class="translate-y-0 transition duration-200 group-hover:-translate-y-3 group-focus:-translate-y-3">
            <div class="mb-2 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-list-check-icon lucide-list-check">
                    <rect width="16" height="20" x="4" y="2" rx="2" />
                    <line x1="8" x2="16" y1="6" y2="6" />
                    <line x1="16" x2="16" y1="14" y2="18" />
                    <path d="M16 10h.01" />
                    <path d="M12 10h.01" />
                    <path d="M8 10h.01" />
                    <path d="M12 14h.01" />
                    <path d="M8 14h.01" />
                    <path d="M12 18h.01" />
                    <path d="M8 18h.01" />
                </svg>
            </div>
            <div class="flex justify-between items-center gap-x-2">
                <h3 class="w-full text-center text-xl md:text-xl font-semibold text-gray-800 dark:text-neutral-200 pb-1">
                    Arvutamine
                </h3>
            </div>
            <h3 class="mt-1 text-md text-gray-500 dark:text-neutral-500">
                Soovin abi elatise summa või võla arvutamisel
            </h3>
        </div>

        <p class="alusta-tekst text-md text-gray-500 underline-offset-4 translate-y-0 transition duration-200 group-hover:translate-y-3 group-focus:translate-y-3 pt-5">
            Alusta siit
        </p>
    </a>

    <!-- LINK - Konsultatsioon -->
    <a class="p-4 md:p-6 min-h-50 group relative overflow-hidden flex flex-col justify-center items-center text-center bg-white border border-gray-200 rounded-xl transition duration-300"
        onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#1f2937'; this.querySelector('.alusta-tekst').style.color='#f97316'; this.querySelector('.alusta-tekst').style.textDecoration='underline'; this.querySelector('.alusta-tekst').style.fontWeight='600';"
        onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color=''; this.querySelector('.alusta-tekst').style.color='#6b7280'; this.querySelector('.alusta-tekst').style.textDecoration='none'; this.querySelector('.alusta-tekst').style.fontWeight='400';" href="<?php echo $rootPath; ?>et/teenused/konsultatsioon.php">
        <div class="translate-y-0 transition duration-200 group-hover:-translate-y-3 group-focus:-translate-y-3">
            <div class="mb-2 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-list-check-icon lucide-list-check">
                    <path d="M14 9a2 2 0 0 1-2 2H6l-4 4V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2z" />
                    <path d="M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1" />
                </svg>
                </svg>
            </div>
            <div class="flex justify-between items-center gap-x-2">
                <h3 class="w-full text-center text-xl md:text-xl font-semibold text-gray-800 dark:text-neutral-200 pb-1">
                    Konsultatsioon
                </h3>
            </div>
            <h3 class="mt-1 text-md text-gray-500 dark:text-neutral-500">
            Soovin juristiga telefoni teel arutada elatisega seotud küsimusi
            </h3>
        </div>

        <p class="alusta-tekst text-md text-gray-500 underline-offset-4 translate-y-0 transition duration-200 group-hover:translate-y-3 group-focus:translate-y-3 pt-5">
            Alusta siit
        </p>
    </a>

    <!-- LINK - Vastus -->
    <a class="p-4 md:p-6 min-h-50 group relative overflow-hidden flex flex-col justify-center items-center text-center bg-white border border-gray-200 rounded-xl transition duration-300"
        onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#1f2937'; this.querySelector('.alusta-tekst').style.color='#f97316'; this.querySelector('.alusta-tekst').style.textDecoration='underline'; this.querySelector('.alusta-tekst').style.fontWeight='600';"
        onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color=''; this.querySelector('.alusta-tekst').style.color='#6b7280'; this.querySelector('.alusta-tekst').style.textDecoration='none'; this.querySelector('.alusta-tekst').style.fontWeight='400';" href="<?php echo $rootPath; ?>et/teenused/vastus.php">
        <div class="translate-y-0 transition duration-200 group-hover:-translate-y-3 group-focus:-translate-y-3">
            <div class="mb-2 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-list-check-icon lucide-list-check">
                <path d="m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7" />
                <rect x="2" y="4" width="20" height="16" rx="2" />
                </svg>
            </div>
            <div class="flex justify-between items-center gap-x-2">
                <h3 class="w-full text-center text-xl md:text-xl font-semibold text-gray-800 dark:text-neutral-200 pb-1">
                    Vastus
                </h3>
            </div>
            <h3 class="mt-1 text-md text-gray-500 dark:text-neutral-500">
            Soovin kirjalikku vastust oma elatisega seotud küsimusele
            </h3>
        </div>

        <p class="alusta-tekst text-md text-gray-500 underline-offset-4 translate-y-0 transition duration-200 group-hover:translate-y-3 group-focus:translate-y-3 pt-5">
            Alusta siit
        </p>
    </a>

    <!-- LINK - Dokument -->
    <a class="p-4 md:p-6 min-h-50 group relative overflow-hidden flex flex-col justify-center items-center text-center bg-white border border-gray-200 rounded-xl transition duration-300"
        onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#1f2937'; this.querySelector('.alusta-tekst').style.color='#f97316'; this.querySelector('.alusta-tekst').style.textDecoration='underline'; this.querySelector('.alusta-tekst').style.fontWeight='600';"
        onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color=''; this.querySelector('.alusta-tekst').style.color='#6b7280'; this.querySelector('.alusta-tekst').style.textDecoration='none'; this.querySelector('.alusta-tekst').style.fontWeight='400';" href="<?php echo $rootPath; ?>et/teenused/dokument.php">
        <div class="translate-y-0 transition duration-200 group-hover:-translate-y-3 group-focus:-translate-y-3">
            <div class="mb-2 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-list-check-icon lucide-list-check">
                    <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
                    <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                    <path d="M10 9H8" />
                    <path d="M16 13H8" />
                    <path d="M16 17H8" />
                </svg>
            </div>
            <div class="flex justify-between items-center gap-x-2">
                <h3 class="w-full text-center text-xl md:text-xl font-semibold text-gray-800 dark:text-neutral-200 pb-1">
                    Dokument
                </h3>
            </div>
            <h3 class="mt-1 text-md text-gray-500 dark:text-neutral-500">
                Soovin juriidiliselt korrektset elatise dokumenti
            </h3>
        </div>

        <p class="alusta-tekst text-md text-gray-500 underline-offset-4 translate-y-0 transition duration-200 group-hover:translate-y-3 group-focus:translate-y-3 pt-5">
            Alusta siit
        </p>
    </a>

    <!-- LINK - Sissenõudmine -->
    <a class="p-4 md:p-6 min-h-50 group relative overflow-hidden flex flex-col justify-center items-center text-center bg-white border border-gray-200 rounded-xl transition duration-300"
        onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#1f2937'; this.querySelector('.alusta-tekst').style.color='#f97316'; this.querySelector('.alusta-tekst').style.textDecoration='underline'; this.querySelector('.alusta-tekst').style.fontWeight='600';"
        onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color=''; this.querySelector('.alusta-tekst').style.color='#6b7280'; this.querySelector('.alusta-tekst').style.textDecoration='none'; this.querySelector('.alusta-tekst').style.fontWeight='400';" href="<?php echo $rootPath; ?>et/teenused/sissenoudmine.php">
        <div class="translate-y-0 transition duration-200 group-hover:-translate-y-3 group-focus:-translate-y-3">
            <div class="mb-2 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-list-check-icon lucide-list-check">
                    <path d="M4 10h12" />
                    <path d="M4 14h9" />
                    <path d="M19 6a7.7 7.7 0 0 0-5.2-2A7.9 7.9 0 0 0 6 12c0 4.4 3.5 8 7.8 8 2 0 3.8-.8 5.2-2" />
                </svg>
            </div>
            <div class="flex justify-between items-center gap-x-2">
                <h3 class="w-full text-center text-xl md:text-xl font-semibold text-gray-800 dark:text-neutral-200 pb-1">
                    Sissenõudmine
                </h3>
            </div>
            <h3 class="mt-1 text-md text-gray-500 dark:text-neutral-500">
            Soovin, et jurist aitaks mul elatist sisse nõuda
            </h3>
        </div>

        <p class="alusta-tekst text-md text-gray-500 underline-offset-4 translate-y-0 transition duration-200 group-hover:translate-y-3 group-focus:translate-y-3 pt-5">
            Alusta siit
        </p>
    </a>

    <!-- LINK - Õigusabi -->
    <a class="p-4 md:p-6 min-h-50 group relative overflow-hidden flex flex-col justify-center items-center text-center bg-white border border-gray-200 rounded-xl transition duration-300"
        onmouseover="this.style.boxShadow='0 8px 30px rgba(0,0,0,0.12)'; this.querySelector('h3').style.color='#1f2937'; this.querySelector('.alusta-tekst').style.color='#f97316'; this.querySelector('.alusta-tekst').style.textDecoration='underline'; this.querySelector('.alusta-tekst').style.fontWeight='600';"
        onmouseout="this.style.boxShadow=''; this.querySelector('h3').style.color=''; this.querySelector('.alusta-tekst').style.color='#6b7280'; this.querySelector('.alusta-tekst').style.textDecoration='none'; this.querySelector('.alusta-tekst').style.fontWeight='400';" href="<?php echo $rootPath; ?>et/teenused/oigusabi.php">
        <div class="translate-y-0 transition duration-200 group-hover:-translate-y-3 group-focus:-translate-y-3">
            <div class="mb-2 flex justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-list-check-icon lucide-list-check">
                    <path d="M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0" />
                    <path d="M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0" />
                </svg>
            </div>
            <div class="flex justify-between items-center gap-x-2">
                <h3 class="w-full text-center text-xl md:text-xl font-semibold text-gray-800 dark:text-neutral-200 pb-1">
                    Õigusabi
                </h3>
            </div>
            <h3 class="mt-1 text-md text-gray-500 dark:text-neutral-500">
                Soovin õigusabi muudes elatise küsimustes
            </h3>
        </div>

        <p class="alusta-tekst text-md text-gray-500 underline-offset-4 translate-y-0 transition duration-200 group-hover:translate-y-3 group-focus:translate-y-3 pt-5">
            Alusta siit
        </p>
    </a>
</div>