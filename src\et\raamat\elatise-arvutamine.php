<!DOCTYPE html>
<html lang="et" class="relative min-h-full light">

<head>
  <!-- Required Meta Tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
  <link rel="canonical" href="https://alimendid.ee/et/teenused/arvutamine.php">
  <meta name="description" content="Seadusele vastav elatise arvutus koos juristi koostatud arvutuskäigu ja selgitustega. Vastus ühe tööpäeva jooksul.">

  <!-- Twitter Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@alimendid">
  <meta name="twitter:creator" content="@alimendid">
  <meta name="twitter:title" content="Juristi koostatud elatise arvutus">
  <meta name="twitter:description" content="Koostame elatise arvutuskäigu ja edastame selgitused ühe tööpäeva jooksul. Teenus sobib kohtule esitamiseks või jagamiseks teise vanemaga.">
  <meta name="twitter:image" content="https://alimendid.ee/assets/failid/pildid/hero/arvutamine.png">

  <!-- Open Graph Meta Tags -->
  <meta property="og:url" content="https://alimendid.ee/et/teenused/arvutamine.php">
  <meta property="og:locale" content="et_EE">
  <meta property="og:type" content="website">
  <meta property="og:site_name" content="Alimendid.ee">
  <meta property="og:title" content="Juristi koostatud elatise arvutus">
  <meta property="og:description" content="Seadusele vastav elatise arvutus koos juristi selgitustega. Tulemuse saad ühe tööpäeva jooksul pärast andmete esitamist.">
  <meta property="og:image" content="https://alimendid.ee/assets/failid/pildid/hero/arvutamine.png">

  <!-- Title -->
  <title>Elatise arvutamine ‧ Alimendid.ee</title>

  <!-- Favicon -->
  <link rel="shortcut icon" href="../../assets/failid/favicon/favicon.ico">

  <!-- Font -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS HS -->
  <link href="../../output.css" rel="stylesheet" />
  <link rel="stylesheet" href="../../assets/css/main.min.css">

  <!-- Theme Check and Update -->
  <script>
    const html = document.querySelector('html');
    const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
    const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
    else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
    else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
    else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
  </script>

  <!-- ANCHOR ‧ Tags -->
  <!-- Google Ads-->
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'UA-221277240-1');
  </script>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-C8JJCED3EQ"></script>
  <script>
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());

    gtag('config', 'G-C8JJCED3EQ');
  </script>

  <!-- Google Tag Manager -->
  <script>
    (function(w, d, s, l, i) {
      w[l] = w[l] || [];
      w[l].push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      });
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : '';
      j.async = true;
      j.src =
        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
      f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'dataLayer', 'GTM-T554HTP');
  </script>

  <!-- Hotjar -->
  <script>
    (function(h, o, t, j, a, r) {
      h.hj = h.hj || function() {
        (h.hj.q = h.hj.q || []).push(arguments)
      };
      h._hjSettings = {
        hjid: 3283746,
        hjsv: 6
      };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script');
      r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
  </script>
</head>

<body class="dark:bg-neutral-900">
  <!-- Google Tag Manager (noscript) -->
  <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T554HTP" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>

  <!-- ========== HEADER ========== -->
  <?php include '../../assets/failid/komponendid/et/menu.php'; ?>
  <!-- ========== END HEADER ========== -->

  <!-- ========== MAIN CONTENT ========== -->
  <main id="content">

    <!-- //SECTION - Hero -->
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 sm:pb-5 md:pb-0">
      <!-- Grid -->
      <div class="grid md:grid-cols-2 md:gap-8 xl:gap-20 md:items-center">
        <!-- Pilt mobiilis esimesena, teistel ekraanidel teisena -->
        <div class="relative ms-4 md:order-2">
          <!-- //LINK ‧ Pilt -->
          <img class="w-full rounded-md" src="../../assets/failid/pildid/raamat/elatise-arvutamine/elatise-arvutamine-hero.webp" alt="Elatise arvutamine">
        </div>
        <!-- End Col -->
        <div class="md:order-1">
          <h1 class="mb-4 block font-semibold text-gray-800 text-4xl md:text-5xl lg:text-6xl leading-none" style="line-height: 1.1;">
            <!-- //LINK ‧ Pealkiri -->
            Elatise arvutamise juhend
          </h1>
          <p class="text-gray-600 md:text-2xl sm:text-2xl">
            <!-- //LINK ‧ Kirjeldus -->
            Lihtne juhend, mis aitab Sul arvutada elatise korrektselt ja kooskõlas seadusega.
          </p>
          <div class="mt-7 grid gap-3 w-full sm:inline-flex">
            <!-- //LINK ‧ Nupp -->
            <a class="py-3 px-4 inline-flex justify-center items-center gap-x-2 text-md text-lg font-semibold rounded-lg border border-transparent bg-orange-500 text-white hover:bg-orange-500 focus:outline-hidden focus:bg-orange-500 disabled:opacity-50 disabled:pointer-events-none" href="<?php echo $rootPath; ?>et/raamat/elatise-arvutamine/andmed.php">
              Osta siit
              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m9 18 6-6-6-6" />
              </svg>
            </a>
            <a class="py-3 px-4 inline-flex justify-center items-center gap-x-2 text-md font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none" href="#sissejuhatus">
              Loe lähemalt
            </a>
          </div>
          <!-- End Buttons -->
        </div>
        <!-- End Col -->
      </div>
      <!-- End Grid -->
    </div>
    <!-- //!SECTION - Hero -->

    <!-- //SECTION - Sissejuhatus -->
    <div id="sissejuhatus" class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
      <!-- Hero -->
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Heading -->
        <div class="mb-8 max-w-xl mx-auto text-center">
          <!-- //LINK ‧ Pealkiri -->
          <h2 class="text-3xl font-semibold md:text-4xl md:leading-tight pb-1">Tutvustus</h2>
          <!-- //LINK ‧ Kirjeldus -->
          <p class="mt-1 text-gray-600 text-lg">Elatise arvutamise juhendi tutvustus</p>
        </div>
        <!-- End Heading -->
      </div>

      <!-- //LINK ‧ Tekst -->
      <div class="max-w-xl mx-auto px-4 text-left text-gray-700 space-y-4">
        <p>Elatise suurus ja selle arvutamine tekitab küsimusi paljudes peredes. Kuigi seadus annab valemi ja olemas on kalkulaatorid, jäävad paljud praktilised olukorrad siiski ebaselgeks. Kuna iga pere olukord on erinev, ei ole üldised juhised sageli piisavad.</p>

        <p>Vaidluse korral otsitakse sageli abi juristilt. Kuna kõikidel peredel ei ole võimalust kasutada tasulist õigusnõustamist, koostasime käesoleva juhendi, et vajalik teave oleks selgelt ja arusaadavalt kättesaadav igale perele.</p>

        <p>Juhend anna tervikliku ülevaate elatise arvutamise õiguslikest alustest, koos selgituste, praktiliste näidete ja täpsete arvutuskäikudega. Juhendi abil saad arvutada seadusjärgse elatise suuruse ja vastused kõigile enda elatisega seotud küsimustele.</p>

        <!-- Slider -->
        <div data-hs-carousel='{
    "loadingClasses": "opacity-0"
  }' class="relative">
        </div>
        <!-- End Slider -->
      </div>
    </div>
    <!-- //!SECTION - Sissejuhatus -->

    <!-- //SECTION - Sissejuhatus -->
    <div id="sissejuhatus" class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
      <!-- Hero -->
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Heading -->
        <div class="mb-8 max-w-xl mx-auto text-center">
          <!-- //LINK ‧ Pealkiri -->
          <h2 class="text-3xl font-semibold md:text-4xl md:leading-tight pb-1">Sisukord</h2>
          <!-- //LINK ‧ Kirjeldus -->
          <p class="mt-1 text-gray-600 text-lg">Elatise arvutamise juhendi sisukord</p>
        </div>
        <!-- End Heading -->
      </div>

      <!-- //LINK ‧ Tekst -->
      <div class="max-w-xl mx-auto px-4 text-left text-gray-700 space-y-4">
        <!-- Slider -->
        <div data-hs-carousel='{
    "loadingClasses": "opacity-0"
  }' class="relative">
          <div class="hs-carousel flex flex-col gap-3 sm:gap-5">
            <!-- Preview -->
            <div class="relative grow overflow-hidden h-137.5 sm:h-175 bg-gray-100 rounded-lg">
              <div class="hs-carousel-body absolute inset-y-0 start-0 flex flex-nowrap opacity-0">
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="../../assets/failid/pildid/raamat/elatise-arvutamine/sisukord/1.jpg" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="../../assets/failid/pildid/raamat/elatise-arvutamine/sisukord/2.jpg" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1598343175492-9e7dc0e63cc6?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1603218190297-df1c6af07965?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1603218162086-fba879e8c17a?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1603218167744-4b6371f208c2?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1598343165919-ba35c5a7743e?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1603218178546-6a870b48be2e?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-slide">
                  <img class="bg-gray-100 size-full object-cover rounded-lg" src="https://images.unsplash.com/photo-1606846859455-1af0b84947f3?q=80&w=560&h=720&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
              </div>

              <!-- Nav - Vasak nupp -->
              <div class="absolute top-1/2 start-4 transform -translate-y-1/2">
                <button type="button" class="hs-carousel-prev hs-carousel-disabled:opacity-50 hs-carousel-disabled:cursor-default inline-flex justify-center items-center size-10 bg-white border border-gray-100 text-gray-800 rounded-full shadow-2xs hover:bg-gray-100 focus:outline-hidden">
                  <span class="text-2xl" aria-hidden="true">
                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m15 18-6-6 6-6" />
                    </svg>
                  </span>
                  <span class="sr-only">Previous</span>
                </button>
              </div>

              <!-- Nav - Parem nupp -->
              <div class="absolute top-1/2 end-4 transform -translate-y-1/2">
                <button type="button" class="hs-carousel-next hs-carousel-disabled:opacity-50 hs-carousel-disabled:cursor-default inline-flex justify-center items-center size-10 bg-white border border-gray-100 text-gray-800 rounded-full shadow-2xs hover:bg-gray-100 focus:outline-hidden">
                  <span class="sr-only">Next</span>
                  <span class="text-2xl" aria-hidden="true">
                    <svg class="shrink-0 size-5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                  </span>
                </button>
              </div>
              <!-- End Nav -->
            </div>
            <!-- End Preview -->

            <!-- Thumbnails -->
            <div class="flex-none">
              <div class="hs-carousel-pagination flex flex-row gap-3 pb-1.5 sm:pb-0 overflow-x-auto sm:overflow-x-hidden sm:overflow-y-auto [&::-webkit-scrollbar]:h-1 sm:[&::-webkit-scrollbar]:hidden [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="../../assets/failid/pildid/raamat/elatise-arvutamine/sisukord/1.jpg" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="../../assets/failid/pildid/raamat/elatise-arvutamine/sisukord/2.jpg" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1598343175492-9e7dc0e63cc6?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1603218190297-df1c6af07965?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1603218162086-fba879e8c17a?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1603218167744-4b6371f208c2?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1598343165919-ba35c5a7743e?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1603218178546-6a870b48be2e?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
                <div class="hs-carousel-pagination-item relative shrink-0 size-20 rounded-md sm:rounded-lg overflow-hidden cursor-pointer after:absolute after:inset-0 after:size-full after:rounded-md sm:after:rounded-lg border border-gray-200 hs-carousel-active:border-gray-800 hs-carousel-active:after:bg-black/10">
                  <img class="bg-gray-100 size-full object-cover rounded-md sm:rounded-lg" src="https://images.unsplash.com/photo-1606846859455-1af0b84947f3?q=80&w=180&h=180&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Product Image">
                </div>
              </div>
            </div>
            <!-- End Thumbnails -->
          </div>
        </div>
        <!-- End Slider -->
      </div>
    </div>
    <!-- //!SECTION - Sissejuhatus -->

    <!-- //SECTION - Ikoonid -->
    <div class="relative py-10 md:pt-20 px-4 before:absolute before:inset-0 before:-z-1 before:rounded-2xl">
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Heading -->
        <div class="mb-8 md:mb-16 max-w-xl mx-auto text-center">
          <h2 class="text-2xl font-semibold md:text-4xl md:leading-tight pb-1">Sisu</h2>
          <p class="mt-1 text-gray-600 text-lg">Elatise arvutamise raamatu sisu</p>
        </div>

        <!-- Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-6 sm:gap-x-10 lg:gap-y-12 lg:gap-x-16">
          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500">
              <rect width="16" height="20" x="4" y="2" rx="2" />
              <line x1="8" x2="16" y1="6" y2="6" />
              <line x1="16" x2="16" y1="14" y2="18" />
              <path d="M16 10h.01" />
              <path d="M12 10h.01" />
              <path d="M8 10h.01" />
              <path d="M12 14h.01" />
              <path d="M8 14h.01" />
              <path d="M12 18h.01" />
              <path d="M8 18h.01" />
            </svg>
            <!-- //LINK ‧ 1 -->
            <div class="grow">
              <h3 class="font-medium text-gray-800 dark:text-neutral-200">
                Elatise arvutus
              </h3>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Arvutame elatise suuruse vastavalt seadusele ja Sinu olukorrale
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500">
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
              <path d="M9 10h6" />
              <path d="M12 13V7" />
              <path d="M9 17h6" />
            </svg>
            <!-- //LINK ‧ 2 -->
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Arvutuskäigu selgitus
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Lisame arvutuskäigu ja selgitused, kuidas elatise summa arvutati
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z" />
              <path d="M14 2v4a2 2 0 0 0 2 2h4" />
              <path d="M10 9H8" />
              <path d="M16 13H8" />
              <path d="M16 17H8" />
            </svg>
            <!-- //LINK ‧ 4 -->
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Dokumentide analüüs
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Kui lisad dokumendid, arvestame neid elatise arvutamisel
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M11 14h1v4" />
              <path d="M16 2v4" />
              <path d="M3 10h18" />
              <path d="M8 2v4" />
              <rect x="3" y="4" width="18" height="18" rx="2" />
            </svg>
            <!-- //LINK ‧ 6 -->
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                1 tööpäeva jooksul
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Saad arvutuse koos selgitusega e-posti teel ühe tööpäeva jooksul
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M16 5a4 3 0 0 0-8 0c0 4 8 3 8 7a4 3 0 0 1-8 0" />
              <path d="M8 19a4 3 0 0 0 8 0c0-4-8-3-8-7a4 3 0 0 1 8 0" />
            </svg>
            <!-- //LINK ‧ 3 -->
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Õiguslik hinnang
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Anname juristi hinnangu koos viidetega seadusele või kohtupraktikale
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

          <!-- Icon Block -->
          <div class="flex gap-5">
            <svg class="shrink-0 size-6 md:size-8 mt-1 text-orange-500 dark:text-orange-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M8 2v4" />
              <path d="M16 2v4" />
              <rect width="18" height="18" x="3" y="4" rx="2" />
              <path d="M3 10h18" />
              <path d="M8 14h.01" />
              <path d="M12 14h.01" />
              <path d="M16 14h.01" />
              <path d="M8 18h.01" />
              <path d="M12 18h.01" />
              <path d="M16 18h.01" />
            </svg>
            <!-- //LINK ‧ 5 -->
            <div class="grow">
              <h4 class="font-medium text-gray-800 dark:text-neutral-200">
                Tagasiulatuv elatis
              </h4>
              <p class="mt-1 text-gray-500 dark:text-neutral-500">
                Vajadusel arvutame ka tasumata jäänud elatisvõla suuruse
              </p>
            </div>
          </div>
          <!-- End Icon Block -->

        </div>
        <!-- End Grid -->
      </div>
    </div>
    <!-- //!SECTION - Mida teenus sisaldab? -->

    
    <!-- //SECTION - 123 -->
    <div class="py-14 lg:py-10 w-full max-w-7xl px-4 sm:px-6 lg:px-8 mx-auto">
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Heading -->
        <div class="mb-8 md:mb-16 max-w-xl mx-auto text-center">
          <h2 class="text-2xl font-semibold md:text-4xl md:leading-tight pb-1">Kuidas raamatut osta?</h2>
          <p class="mt-1 text-gray-600 text-lg">Raamatu ostmine on lihtne ja kiire</p>
        </div>
        <!-- End Heading -->
      </div>
      <!-- Grid -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-y-10 gap-x-4">
        <!-- Icon Block -->
        <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class=" text-orange-500 mx-auto lucide lucide-clipboard-pen-icon lucide-clipboard-pen">
            <rect width="8" height="4" x="8" y="2" rx="1" />
            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5.5" />
            <path d="M4 13.5V6a2 2 0 0 1 2-2h2" />
            <path d="M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z" />
          </svg>
          <!-- //LINK ‧ 1 -->
          <div class="mt-2 sm:mt-2">
            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
              1. Sisesta andmed
            </h3>
          </div>
          <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
            Kirjuta oma nimi ja e-posti aadress, et saaksime Sulle raamatu saata
          </p>
        </div>
        <!-- End Icon Block -->

        <!-- Icon Block -->
        <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class="text-orange-500 mx-auto lucide lucide-calculator-icon lucide-calculator">
            <path d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1" />
            <path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4" />
          </svg>
          <!-- //LINK ‧ 2 -->
          <div class="mt-2 sm:mt-2">
            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
              2. Maksa turvaliselt
            </h3>
          </div>
          <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
            Tasu raamatu eest turvaliselt pangalingi vahendusel
          </p>
        </div>
        <!-- End Icon Block -->

        <!-- Icon Block -->
        <div class="max-w-xs lg:max-w-full mx-auto text-center lg:px-4 xl:px-10">
          <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" class="text-orange-500 mx-auto lucide lucide-mail-check-icon lucide-mail-check">
            <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20" />
            <path d="m9 9.5 2 2 4-4" />
          </svg>
          <!-- //LINK ‧ 3 -->
          <div class="mt-2 sm:mt-2">
            <h3 class="sm:text-lg md:text-xl font-semibold text-gray-800">
              3. Lae alla
            </h3>
          </div>
          <p class="mt-2 text-md text-gray-500 dark:text-neutral-500">
            Maksmise järel saadame raamatu Sinu e-posti aadressile
          </p>
        </div>
        <!-- End Icon Block -->
      </div>
      <!-- End Grid -->
    </div>
    <!-- Jooned -->
    <div class="mx-auto mb-3 hidden md:flex justify-center pointer-events-none">
      <img class="img-fluid" src="../../assets/failid/svg/jooned.svg" alt="Jooned">
    </div>
    <!-- //!SECTION - Kuidas teenus töötab? -->

    <!-- //SECTION - Hind -->
    <div
      class="relative py-10 md:py-14 px-4 before:absolute before:inset-0 before:-z-1">
      <div class="max-w-4xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- Heading -->
        <div class="mb-8 md:mb-16 max-w-xl mx-auto text-center">
          <h2 class="text-2xl font-semibold md:text-4xl md:leading-tight pb-1">Hind</h2>
          <p class="mt-1 text-gray-600 text-lg">Elatise arvutamise raamatu hind</p>
        </div>
        <!-- End Heading -->
      </div>
      <div class="max-w-6xl px-4 sm:px-6 lg:px-8 mx-auto">
        <!-- //LINK ‧ Link -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 lg:gap-5">
          <!-- Tühi div esimese veeru jaoks -->
          <div class="hidden lg:block"></div>

          <?php include '../../assets/failid/komponendid/et/hinnad/raamat-elatise-arvutamine.php'; ?>

          <!-- Tühi div kolmanda veeru jaoks -->
          <div class="hidden lg:block"></div>
        </div>
        <div class="mt-6 md:mt-10 text-center relative z-10">
          <!-- Footer -->
          <p class="mb-5 text-xs sm:text-sm text-gray-800 dark:text-neutral-400">
            Mõnel juhul võib osutuda vajalikuks mitme teenuse kasutamine.
          </p>

          <div class="flex flex-wrap justify-center items-center gap-2">
            <h5 class="text-sm font-medium text-gray-800 dark:text-neutral-200">
              Tasutud teenustasu arvestame maha järgmise teenuse hinnast.
            </h5>
            <a href="../../et/alusta.php" class="inline-flex items-center gap-x-1 text-sm font-medium text-orange-500 decoration-1 hover:underline focus:outline-hidden focus:underline dark:text-orange-500">
              Vaata kõiki teenuseid
              <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m9 18 6-6-6-6" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- //!SECTION - Hind -->

  </main>
  <!-- ========== END MAIN CONTENT ========== -->

  <!-- //LINK - Footer -->
  <?php include '../../assets/failid/komponendid/et/footer.php'; ?>

  <!-- JS PLUGINS -->
  <!-- Required plugins -->
  <script src="../../assets/vendor/lodash/lodash.min.js"></script>
  <script src="../../assets/vendor/preline/dist/index.js?v=3.0.1"></script>
  <!-- Clipboard -->
  <script src="../../assets/vendor/clipboard/dist/clipboard.min.js"></script>
  <script src="../../assets/js/hs-copy-clipboard-helper.js"></script>


</body>

</html>